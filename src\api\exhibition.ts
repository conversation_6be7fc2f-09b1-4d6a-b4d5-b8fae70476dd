import request from './request'

/**
 * 资产相关API接口
 */

// 获取系列列表
export const getSeriesList = () => {
  return request.get('/series/series/listName')
}

// 获取专区列表
export const getZonesList = () => {
  return request.get('/zone/zone/listName')
}

/**
 * 资产接口参数类型
 */
export interface AssetListParams {
  assetName?: string    // 资产名称搜索
  seriesId?: number     // 系列ID (Long类型)
  zoneId?: number       // 专区ID (Long类型)
  assetType?: string    // 资产类型
  page?: number         // 页码
  size?: number         // 每页数量
}

/**
 * 发行方信息类型
 */
export interface IssuerInfo {
  id?: string | number               // 发行方ID
  issuerName?: string                // 发行方名称
  name?: string                      // 通用名称字段
  issuerLogo?: string                // 发行方Logo URL
  logo?: string                      // 通用Logo字段
  [key: string]: any                 // 允许其他字段
}

/**
 * 资产数据类型 - 基于后端 DigDigitalAsset 实体类
 */
export interface AssetItem {
  // 主要字段 - 对应后端实体
  assetId: number | string           // 资产ID，主键
  assetName: string                  // 资产名称
  assetCover?: string                // 资产封面图URL
  assetFile?: string                 // 资产文件URL
  issuerIds?: string                 // 发行方ID列表
  issuers?: IssuerInfo[]             // 发行方信息列表（从接口返回）
  assetType?: string                 // 资产类型
  assetLevel?: number                // 资产等级
  saleStartTime?: string             // 开售时间
  issueQuantity?: number             // 发行数量
  individualLimit?: number           // 个人限购数量
  enterpriseLimit?: number           // 企业限购数量
  airdropQuantity?: number           // 空投数量
  activityQuantity?: number          // 活动数量
  issuePrice?: number | string       // 发行价格
  seriesId?: number | string         // 所属系列ID
  assetKeywords?: string             // 资产关键字
  assetDesc?: string                 // 资产简介
  introImages?: string               // 资产介绍图片URL
  rejectionReason?: string           // 驳回原因
  statusCd?: string                  // 状态代码
  statusDate?: string                // 状态时间
  createStaff?: string               // 创建人
  createDate?: string                // 创建时间
  updateStaff?: string               // 修改人
  updateDate?: string                // 修改时间
  remark?: string                    // 备注

  // 兼容字段 - 保持向后兼容
  id?: string | number               // 通用ID字段
  name?: string                      // 通用名称字段
  image?: string                     // 通用图片字段
  price?: string                     // 通用价格字段
  category?: string                  // 分类
  categoryName?: string              // 分类名称
  seriesName?: string                // 系列名称
  zoneId?: string                    // 专区ID
  zoneName?: string                  // 专区名称
  saleTime?: string                  // 通用销售时间
  stats?: string                     // 统计信息
  popularity?: number                // 热度
  createTime?: string                // 通用创建时间
  
  // 展示相关字段
  tags?: Array<{
    text: string
    type: 'primary' | 'accent' | 'success' | 'warning'
  }>
  tag?: {
    text: string
    type: 'on-sale' | 'limited' | 'sold-out' | 'default'
  }
  
  [key: string]: any  // 允许其他字段
}

/**
 * 获取资产详情
 */
export const getAssetDetail = async (assetId: string | number) => {
  const url = `/asset/asset/client/detail/${assetId}`
  console.log('资产详情请求URL:', url)
  
  return request.get(url)
}

/**
 * 获取资产列表
 */
export const getAssetList = async (params: AssetListParams = {}) => {
  const queryParams = new URLSearchParams()
  
  // 添加搜索参数
  if (params.assetName?.trim()) {
    queryParams.append('assetName', params.assetName.trim())
  }
  
  // 添加筛选参数
  if (params.seriesId !== undefined && params.seriesId !== null) {
    queryParams.append('seriesId', String(params.seriesId))
  }
  
  if (params.zoneId !== undefined && params.zoneId !== null) {
    queryParams.append('zoneId', String(params.zoneId))
  }
  
  if (params.assetType?.trim()) {
    queryParams.append('assetType', params.assetType.trim())
  }
  
  // 添加分页参数
  queryParams.append('page', String(params.page || 1))
  queryParams.append('size', String(params.size || 20))
  
  const url = `/asset/asset/client/list${queryParams.toString() ? '?' + queryParams.toString() : ''}`
  console.log('资产列表请求URL:', url)
  
  return request.get(url)
}

export default {
  getSeriesList,
  getZonesList,
  getAssetList,
  getAssetDetail
} 