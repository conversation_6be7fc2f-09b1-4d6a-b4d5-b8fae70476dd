import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import CouponListView from '@/views/CouponListView.vue'
import ResetPasswordView from '@/views/ResetPasswordView.vue'
import BlindBoxListView from '@/views/BlindBoxListView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
      meta: {
        title: '首页 - 凌云数资'
      }
    },
    {
      path: '/about',
      name: 'about',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/AboutView.vue'),
      meta: {
        title: '关于我们 - 凌云数资'
      }
    },
    {
      path: '/design-system',
      name: 'design-system',
      component: () => import('../views/DesignSystemView.vue'),
    },
    {
      path: '/example',
      name: 'example',
      component: () => import('../views/ExampleView.vue'),
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: {
        title: '登录 - 凌云数资'
      }
    },
    {
      path: '/forgot-password',
      name: 'forgot-password',
      component: () => import('../views/ForgotPasswordView.vue'),
      meta: {
        title: '忘记密码 - 凌云数资'
      }
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/RegisterView.vue'),
      meta: {
        title: '注册 - 凌云数资'
      }
    },
    {
      path: '/invite-register',
      name: 'invite-register',
      component: () => import('../views/InviteRegisterView.vue'),
      meta: {
        title: '邀请注册 - 凌云数资'
      }
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/ProfileView.vue'),
      meta: {
        title: '个人中心 - 凌云数资'
      }
    },
    {
      path: '/verification',
      name: 'verification',
      component: () => import('../views/VerificationView.vue'),
      meta: {
        title: '实名认证 - 凌云数资'
      }
    },
    {
      path: '/presale',
      name: 'presale',
      component: () => import('../views/PresaleView.vue'),
      meta: {
        title: '预售专区 - 凌云数资'
      }
    },
    {
      path: '/exhibition',
      name: 'exhibition',
      component: () => import('../views/ExhibitionView.vue'),
      meta: {
        title: '数字资产 - 凌云数资'
      }
    },
    {
      path: '/asset/:assetId',
      name: 'asset-detail',
      component: () => import('../views/AssetDetailView.vue'),
      meta: {
        title: '资产详情 - 凌云数资'
      }
    },
    {
      path: '/series/:seriesId',
      name: 'series-detail',
      component: () => import('../views/SeriesView.vue'),
      meta: {
        title: '系列详情 - 凌云数资'
      }
    },
    {
      path: '/zone/:zoneId',
      name: 'zone-detail',
      component: () => import('../views/ZoneView.vue'),
      meta: {
        title: '专区详情 - 凌云数资'
      }
    },
    {
      path: '/zones',
      name: 'zones',
      component: () => import('../views/ZonesView.vue'),
      meta: {
        title: '专区 - 凌云数资'
      }
    },
    {
      path: '/activities',
      name: 'activities',
      component: () => import('../views/ActivitiesView.vue'),
      meta: {
        title: '活动列表 - 凌云数资'
      }
    },
    {
      path: '/activity/:activityId',
      name: 'activity-detail',
      component: () => import('../views/ActivityDetailView.vue'),
      meta: {
        title: '活动详情 - 凌云数资'
      }
    },
    {
      path: '/activity/:activityId/invite-rank',
      name: 'invite-rank',
      component: () => import('../views/InviteRankView.vue'),
      meta: {
        title: '邀新排行榜 - 凌云数资'
      }
    },
    {
      path: '/search',
      name: 'search-results',
      component: () => import('../views/SearchResultsView.vue'),
      meta: {
        title: '搜索结果 - 凌云数资'
      }
    },
    {
      path: '/payment/:orderNo',
      name: 'payment',
      component: () => import('../views/PaymentView.vue'),
      meta: {
        title: '确认支付 - 凌云数资'
      }
    },
    {
      path: '/my-orders',
      name: 'my-orders',
      component: () => import('../views/MyOrdersView.vue'),
      meta: {
        title: '我的订单 - 凌云数资'
      }
    },
    {
      path: '/my-assets',
      name: 'my-assets',
      component: () => import('../views/MyAssetsView.vue'),
      meta: {
        title: '我的藏品 - 凌云数资'
      }
    },
    {
      path: '/asset-detail/:dataAssetId',
      name: 'asset-detail-new',
      component: () => import('../views/AssetDetailView.vue'),
      meta: {
        title: '资产详情 - 凌云数资'
      }
    },
    {
      path: '/asset-model/:assetId',
      name: 'asset-model',
      component: () => import('../views/AssetModelView.vue'),
      meta: {
        title: '资产模型 - 凌云数资'
      }
    },
    {
      path: '/settings',
      name: 'settings',
      component: () => import('../views/SettingsView.vue'),
      meta: {
        title: '设置 - 凌云数资'
      }
    },
    {
      path: '/purchased-asset/:dataAssetId',
      name: 'purchased-asset',
      component: () => import('../views/PurchasedAssetView.vue'),
      meta: {
        title: '我的资产 - 凌云数资'
      }
    },
    {
      path: '/coupon-list',
      name: 'coupon-list',
      component: CouponListView,
      meta: {
        title: '权益券列表 - 凌云数资'
      }
    },
    {
      path: '/reset-password',
      name: 'reset-password',
      component: ResetPasswordView,
      meta: { title: '重置密码' }
    },
    {
      path: '/privacy-policy',
      name: 'privacy-policy',
      component: () => import('@/views/PrivacyPolicyView.vue'),
      meta: { title: '隐私政策 - 凌云数资' }
    },
    {
      path: '/user-agreement',
      name: 'user-agreement',
      component: () => import('@/views/UserAgreementView.vue'),
      meta: { title: '用户协议 - 凌云数资' }
    },
    {
      path: '/agreement-list',
      name: 'agreement-list',
      component: () => import('@/views/AgreementListView.vue'),
      meta: { title: '协议与政策 - 凌云数资' }
    },
    {
      path: '/blind-boxes',
      name: 'blind-boxes',
      component: BlindBoxListView,
      meta: { title: '我的盲盒' }
    },
  ],
})

// 路由守卫：设置页面标题
router.beforeEach((to, from, next) => {
  if (to.meta?.title) {
    document.title = to.meta.title as string
  }
  next()
})

export default router
