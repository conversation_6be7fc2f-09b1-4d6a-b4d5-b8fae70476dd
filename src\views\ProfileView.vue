<template>
  <div class="profile-page with-bottom-nav">
    <main class="main-content">
      <!-- 用户信息 - 高端设计 -->
      <section class="user-profile premium-style">
        <div class="user-avatar-container">
          <div class="avatar-ring">
            <div class="avatar-glow"></div>
            <div class="user-avatar">
              <img v-if="userInfo.avatar" :src="userInfo.avatar" :alt="userInfo.name" class="avatar-image">
              <i v-else class="fas fa-user default-avatar"></i>
            </div>
          </div>
        </div>
        <div class="user-info">
          <div class="user-name">
            {{ userInfo.nickName }}
            <span class="verification-tag" :class="{ 'verified': isVerified, 'unverified': !isVerified }">
              <i :class="isVerified ? 'fas fa-check-circle' : 'fas fa-times-circle'"></i>
              {{ isVerified ? '已实名' : '未实名' }}
            </span>
          </div>
          <div class="user-points">
            <i class="fas fa-coins points-icon"></i>
            <span class="points-text">{{ userPoints }} 积分</span>
          </div>
          <div class="user-address" @click="handleAddressClick">
            <div class="address-content">
              <i :class="getAddressIcon()" class="address-icon"></i>
              <span class="address-text" :class="getAddressTextClass()">{{ formatWalletAddress(userInfo.walletAddress) }}</span>
              <span v-if="canCopyAddress()" class="address-label">复制</span>
            </div>
            <i v-if="canCopyAddress()" class="fas fa-chevron-right"></i>
          </div>
        </div>
      </section>

<!-- 快捷操作区域 -->
<section class="quick-actions">
  <div class="action-grid">
    <button class="action-item" @click="navigateTo('/my-orders')">
      <div class="action-icon">
        <i class="fas fa-receipt"></i>
      </div>
      <div class="action-label">订单</div>
    </button>
    
    <button class="action-item" @click="handleMembershipClick">
      <div class="action-icon">
        <i class="fas fa-crown"></i>
      </div>
      <div class="action-label">会员</div>
    </button>
    
    <button class="action-item" @click="navigateTo('/activities')">
      <div class="action-icon">
        <i class="fas fa-gift"></i>
      </div>
      <div class="action-label">活动</div>
    </button>
    
    <button class="action-item" @click="navigateTo('/settings')">
      <div class="action-icon">
        <i class="fas fa-cog"></i>
      </div>
      <div class="action-label">设置</div>
    </button>
    <button class="action-item" @click="navigateTo('/blind-boxes')">
      <div class="action-icon">
        <i class="fas fa-cube"></i>
      </div>
      <div class="action-label">盲盒</div>
    </button>
  </div>
</section>

      <!-- 资产总览卡片 -->
      <section class="assets-overview">
        <div class="assets-header">
          <div class="assets-title">
            <i class="fas fa-eye assets-eye" :class="{ 'eye-closed': !showAssets }" @click="toggleAssetsVisibility"></i>
            <span>我的数字资产</span>
          </div>
          <div class="rewards-info" @click="goToCouponList" style="cursor:pointer;">
            <span class="rewards-text">我的权益券</span>
            <span class="rewards-count">{{ couponCount }}张</span>
            <i class="fas fa-chevron-right"></i>
          </div>
        </div>
        
        <!-- 资产卡片列表 -->
        <div class="asset-card-list">
          <div v-for="asset in assetList" :key="asset.dataAssetId" class="asset-card" @click="viewAssetDetail(asset)">
            <div class="asset-image-wrapper">
              <img :src="asset.assetCoverThumbnail" :alt="asset.assetName" class="asset-image" />
            </div>
            <div class="asset-info">
              <div class="asset-name">{{ asset.assetName }}</div>
              <div class="asset-code"><i class="fas fa-cog"></i> {{ asset.assetCode }}</div>
            </div>
          </div>
          <div v-if="loadingMore" class="text-center py-4">加载中...</div>
          <div v-if="assetList.length === 0 && !loadingMore" class="text-center py-4">暂无资产</div>
          <div v-if="assetList.length > 0 && assetList.length < total && !loadingMore" class="text-center py-4">
            <button @click="handleLoadMore" class="btn btn-primary btn-sm">加载更多</button>
          </div>
        </div>
      </section>



    </main>

    <!-- 底部导航组件 -->
    <BottomNavigation />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useNotification } from '@/composables/useNotification'
import AuthAPI from '@/api/auth'
import UserAPI, { type UserAssetNum, type UserPoints } from '@/api/user'
import BottomNavigation from '@/components/BottomNavigation.vue'
import type { AssetData } from '@/api/user'
import { getUserCouponCount } from '@/api/coupon'
import { request } from '@/api/request'

// 响应式数据
const router = useRouter()

// 通知系统
const { success, error, info } = useNotification()

// 用户信息
const userInfo = reactive({
  name: '收藏家16709795526190',
  nickName: '收藏家16709795526190',
  id: '272',
  level: 'VIP会员',
  avatar: '', // 用户头像URL，为空时显示默认头像
  phonenumber: '17360134067',
  walletAddress: '******************************************' // 用户的链上钱包地址
})

// 统计数据
const stats = reactive({
  collections: 12,
  transactions: 5,
  totalValue: '2,680'
})

// 控制资产显示/隐藏
const showAssets = ref(true)

// 资产卡片数据
const assetList = ref<AssetData[]>([])
const pageNum = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loadingMore = ref(false)

// 权益券数量
const couponCount = ref(0)

// 用户积分
const userPoints = ref(0)

// 实名认证状态
const isVerified = ref(false)

/**
 * 格式化手机号
 */
const formatPhoneNumber = (phone: string): string => {
  console.log(phone)
  if (!phone || phone.length < 11) return phone
  return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3')
}

/**
 * 格式化钱包地址
 */
const formatWalletAddress = (address: string): string => {
  // 如果是状态文本（未实名认证、地址生成中），直接返回
  if (address === '未实名认证' || address === '地址生成中') {
    return address
  }
  
  // 如果是真实地址，则格式化显示
  if (!address || address.length < 10) return address
  return `${address.slice(0, 6)}...${address.slice(-4)}`
}

/**
 * 切换资产显示状态
 */
const toggleAssetsVisibility = () => {
  showAssets.value = !showAssets.value
}

/**
 * 处理地址点击
 */
const handleAddressClick = () => {
  // 可以跳转到钱包详情页面或复制地址
  handleCopyAddress()
}

/**
 * 复制钱包地址到剪贴板
 */
const handleCopyAddress = async () => {
  // 如果是状态文本，不执行复制操作
  if (userInfo.walletAddress === '未实名认证' || userInfo.walletAddress === '地址生成中') {
    if (userInfo.walletAddress === '未实名认证') {
      info('请先完成实名认证')
      //跳转到实名认证页面
      router.push('/verification')
    } else {
      info('钱包地址正在生成中，请稍后再试')
    }
    return
  }
  
  try {
    await navigator.clipboard.writeText(userInfo.walletAddress)
    success('钱包地址已复制到剪贴板')
  } catch (err) {
    // 备用复制方法
    const textArea = document.createElement('textarea')
    textArea.value = userInfo.walletAddress
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    success('钱包地址已复制到剪贴板')
  }
}

/**
 * 会员按钮点击提示
 */
const handleMembershipClick = () => {
  info('功能暂未开放')
}

/**
 * 导航到指定页面
 */
const navigateTo = (path: string) => {
  // 特殊页面路由映射
  const routeMap: Record<string, string> = {
    '/verify': '/verification'
  }
  
  const targetPath = routeMap[path] || path
  
  // 已实现的页面直接跳转
  if (targetPath === '/verification') {
    router.push(targetPath)
  } else if (targetPath === '/my-orders') {
    router.push('/my-orders')
  } else if (targetPath === '/my-assets') {
    router.push('/my-assets')
  } else if (targetPath === '/about') {
    router.push('/about')
  } else if (targetPath === '/activities') {
    router.push('/activities')
  } else if (targetPath === '/settings') {
    router.push('/settings')
  } else if (targetPath === '/membership') {
    router.push('/membership')
  } else if (targetPath === '/blind-boxes') {
    router.push('/blind-boxes')
  } else {
    // TODO: 实现其他页面路由
    console.log('Page not implemented yet:', targetPath)
  }
}

/**
 * 获取页面标题
 */
const getPageTitle = (path: string): string => {
  const titleMap: Record<string, string> = {
    '/my-collection': '我的收藏',
    '/my-orders': '我的订单',
    '/transactions': '交易记录',
    '/transfer': '转赠藏品',
    '/verify': '实名认证',
    '/security': '安全设置',
    '/notifications': '消息通知',
    '/about': '关于我们',
    '/help': '帮助中心',
    '/customer-service': '客服支持',
    '/privacy': '隐私政策'
  }
  return titleMap[path] || '未知页面'
}

/**
 * 加载用户数据
 */
const loadUserData = async () => {
  try {
    // 检查是否已登录
    if (!AuthAPI.isLoggedIn()) {
      router.push('/login')
      return
    }
    
    // 获取本地用户信息
    const localUserInfo = AuthAPI.getLocalUserInfo()
    if (localUserInfo) {
      userInfo.name = localUserInfo.nickName || localUserInfo.userName || '川蜀收藏家'
      userInfo.nickName = localUserInfo.nickName || localUserInfo.userName || '川蜀收藏家'
      userInfo.id = localUserInfo.userId?.toString() || '1001234567'
      userInfo.avatar = localUserInfo.avatar || ''
    }
    
    // 获取用户统计数据
    // try {
    //   const statsResponse = await UserAPI.getUserStats()
    //   if (statsResponse.code === 200 && statsResponse.data) {
    //     stats.collections = statsResponse.data.collections
    //     stats.transactions = statsResponse.data.transactions
    //     stats.totalValue = statsResponse.data.totalValue
    //   }
    // } catch (statsErr) {
    //   console.warn('Failed to load user stats:', statsErr)
    //   // 使用默认值，不显示错误
    // }
    
    // 获取用户资产数量信息
    // try {
    //   const assetResponse = await UserAPI.getUserAssetNum()
    //   if (assetResponse.code === 200 && assetResponse.data) {
    //     // 更新用户链上地址
    //     userInfo.walletAddress = assetResponse.data.userAddress
    //     // 更新资产数量
    //     stats.collections = assetResponse.data.assetQuantity
    //   }
    // } catch (assetErr) {
    //   console.warn('Failed to load asset info:', assetErr)
    //   // 保持默认值，不显示错误
    // }
    
    // // 获取用户钱包信息
    // try {
    //   const walletResponse = await UserAPI.getUserWallet()
    //   if (walletResponse.code === 200 && walletResponse.data) {
    //     userInfo.walletAddress = walletResponse.data.address
    //   }
    // } catch (walletErr) {
    //   console.warn('Failed to load wallet info:', walletErr)
    //   // 保持默认的钱包地址
    // }
    
  } catch (err) {
    console.error('Load user data error:', err)
    error('加载用户数据失败')
  }
}

/**
 * 加载用户资产列表
 */
const loadUserAssets = async (append = false) => {
  try {
    loadingMore.value = true
    const res = await UserAPI.getUserAssetList({ pageNum: pageNum.value, pageSize: pageSize.value })
    if (res.code === 200 && Array.isArray(res.rows)) {
      if (append) {
        assetList.value = assetList.value.concat(res.rows)
      } else {
        assetList.value = res.rows
      }
      total.value = res.total || 0
    } else {
      if (!append) assetList.value = []
    }
  } catch (err) {
    if (!append) assetList.value = []
    console.error('加载用户资产失败', err)
  } finally {
    loadingMore.value = false
  }
}

/**
 * 加载用户权益券数量
 */
const loadUserCouponCount = async () => {
  try {
    const res = await getUserCouponCount()
    if (typeof res.data === 'number') {
      couponCount.value = res.data
    } else {
      couponCount.value = 0
    }
  } catch (err) {
    couponCount.value = 0
    console.error('加载权益券数量失败', err)
  }
}

/**
 * 加载用户积分
 */
const loadUserPoints = async () => {
  try {
    const response = await UserAPI.getUserPoints()
    if (response.code === 200 && response.data) {
      userPoints.value = response.data.points || 0
      console.log('✅ 用户积分加载成功:', userPoints.value)
    } else {
      userPoints.value = 0
      console.warn('⚠️ 积分接口返回空数据')
    }
  } catch (err) {
    console.error('❌ 获取用户积分失败:', err)
    userPoints.value = 0
  }
}

/**
 * 获取实名认证状态
 */
const loadVerificationStatus = async () => {
  try {
    const response: any = await request.get('/identify/isIdentify')
    // 接口直接返回true或false
    isVerified.value = response.data
    
    // 如果已实名认证，则获取钱包地址
    if (isVerified.value) {
      await loadUserWalletAddress()
    } else {
      // 未实名认证，显示默认提示
      userInfo.walletAddress = '未实名认证'
    }
  } catch (err) {
    console.error('获取实名认证状态失败:', err)
    isVerified.value = false
    userInfo.walletAddress = '未实名认证'
  }
}

/**
 * 获取用户钱包地址
 */
const loadUserWalletAddress = async () => {
  try {
    const response: any = await request.get('/identify/getUserAddress')
    if (response.data && response.data.trim() !== '') {
      // 有地址则显示地址
      userInfo.walletAddress = response.data
    } else {
      // 地址为空则显示生成中
      userInfo.walletAddress = '地址生成中'
    }
  } catch (err) {
    console.error('获取钱包地址失败:', err)
    userInfo.walletAddress = '地址生成中'
  }
}

/**
 * 获取地址图标
 */
const getAddressIcon = () => {
  if (userInfo.walletAddress === '未实名认证') {
    return 'fas fa-exclamation-triangle'
  } else if (userInfo.walletAddress === '地址生成中') {
    return 'fas fa-spinner fa-spin'
  } else {
    return 'fas fa-wallet'
  }
}

/**
 * 获取地址文本样式类
 */
const getAddressTextClass = () => {
  if (userInfo.walletAddress === '未实名认证') {
    return 'address-text-warning'
  } else if (userInfo.walletAddress === '地址生成中') {
    return 'address-text-loading'
  } else {
    return ''
  }
}

/**
 * 判断是否可以复制地址
 */
const canCopyAddress = () => {
  return userInfo.walletAddress !== '未实名认证' && userInfo.walletAddress !== '地址生成中'
}

const handleLoadMore = () => {
  if (assetList.value.length < total.value && !loadingMore.value) {
    pageNum.value++
    loadUserAssets(true)
  }
}

/**
 * 查看资产详情
 */
const viewAssetDetail = (asset: AssetData) => {
  router.push({
    name: 'purchased-asset',
    params: { dataAssetId: asset.dataAssetId.toString() }
  })
}

const goToCouponList = () => {
  router.push('/coupon-list')
}

// 生命周期钩子
onMounted(() => {
  loadUserData()
  loadUserAssets()
  loadUserCouponCount()
  loadUserPoints()
  loadVerificationStatus()
})
</script>

<style scoped>
/* CSS变量定义 - 深色金黄主题配色方案 */
.profile-page {
  /* 主色系 - 优雅金黄 */
  --primary-color: #d4a574;
  --primary-dark: #b8956a;
  --primary-light: #e8c49a;
  --primary-alpha: rgba(212, 165, 116, 0.15);
  
  /* 辅助色系 - 温暖橙色 */
  --accent-color: #f4a261;
  --accent-dark: #e76f51;
  --accent-light: #f9c74f;
  --accent-alpha: rgba(244, 162, 97, 0.15);
  
  /* 功能色系 */
  --success-color: #90a955;
  --warning-color: #f9844a;
  --error-color: #e63946;
  --info-color: #457b9d;
  
  /* 文字颜色系统 */
  --text-primary: #f8f6f0;
  --text-secondary: #e0ddd4;
  --text-tertiary: #c4c0b1;
  --text-muted: #a39d8e;
  --text-inverse: #2d2a24;
  
  /* 背景色系统 */
  --bg-primary: #1a1a1a;
  --bg-secondary: #242424;
  --bg-tertiary: #2e2e2e;
  --bg-card: rgba(42, 42, 42, 0.9);
  --bg-card-hover: rgba(50, 50, 50, 0.95);
  --bg-glass: rgba(42, 42, 42, 0.8);
  
  /* 边框和阴影 */
  --border-primary: rgba(212, 165, 116, 0.3);
  --border-light: rgba(248, 246, 240, 0.1);
  --border-hover: rgba(212, 165, 116, 0.5);
  
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.6);
  --shadow-primary: 0 4px 16px rgba(212, 165, 116, 0.3);
  --shadow-glow: 0 0 20px rgba(212, 165, 116, 0.4);
  
  /* 渐变系统 */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
  --gradient-bg: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
  --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(46, 46, 46, 0.8) 100%);
  --gradient-hero: linear-gradient(180deg, rgba(26, 26, 26, 0.8) 0%, rgba(36, 36, 36, 0.9) 50%, rgba(46, 46, 46, 0.95) 100%);
  --gradient-glow: linear-gradient(135deg, rgba(212, 165, 116, 0.3) 0%, rgba(244, 162, 97, 0.2) 100%);
  --gradient-glass: rgba(255, 255, 255, 0.1);
  
  /* 兼容旧变量名 */
  --primary-gold: var(--primary-color);
  --primary-gold-light: var(--primary-light);
  --primary-gold-dark: var(--primary-dark);
  --primary-gold-alpha: var(--primary-alpha);
  --secondary-purple: var(--accent-color);
  --secondary-purple-light: var(--accent-light);
  --secondary-purple-dark: var(--accent-dark);
  --neutral-100: var(--text-primary);
  --neutral-200: var(--text-secondary);
  --neutral-400: var(--text-tertiary);
  --neutral-600: var(--text-muted);
  --accent-red: var(--error-color);
  --shadow-gold: var(--shadow-primary);
  --shadow-purple: var(--shadow-primary);
  
  /* 页面样式 */
  background: var(--gradient-hero);
  color: var(--neutral-100);
  font-size: 14px;
  line-height: 1.6;
  min-height: 100vh;
  padding: 0;
  margin: 0;
  overflow-y: visible !important;
  height: auto !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  position: relative;
  /* 确保页面不会影响底部导航的fixed定位 */
  transform: none;
  will-change: auto;
}

.main-content {
  /* 为底部固定导航预留空间，防止内容被遮挡 */
  padding: 20px 16px 20px 16px;
  position: relative;
  z-index: 1;
  /* 确保内容不会创建新的层叠上下文，影响底部导航 */
  transform: none;
}

/* 用户信息区域 - 高端设计 */
.user-profile.premium-style {
  background: linear-gradient(145deg, rgba(42, 42, 42, 0.95) 0%, rgba(46, 46, 46, 0.9) 100%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  margin: 20px 0;
  display: flex;
  align-items: center;
  gap: 20px;
  border: 1px solid rgba(212, 165, 116, 0.2);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.6),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;
}

.user-profile.premium-style::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 165, 116, 0.1), transparent);
  animation: light-sweep 4s infinite ease-in-out;
}

@keyframes light-sweep {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: 100%; }
}

.user-profile.premium-style:hover {
  transform: translateY(-3px);
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.7),
    0 0 30px rgba(212, 165, 116, 0.3),
    0 0 0 1px rgba(212, 165, 116, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  border-color: rgba(212, 165, 116, 0.4);
}

.user-avatar-container {
  position: relative;
  width: 80px;
  height: 80px;
  flex-shrink: 0;
}

.avatar-ring {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--primary-color), var(--primary-light), var(--primary-color));
  padding: 3px;
  box-shadow: 
    0 0 20px rgba(212, 165, 116, 0.5),
    inset 0 0 20px rgba(212, 165, 116, 0.2);
  animation: glow-pulse 3s ease-in-out infinite;
}

@keyframes glow-pulse {
  0%, 100% { box-shadow: 0 0 20px rgba(212, 165, 116, 0.5), inset 0 0 20px rgba(212, 165, 116, 0.2); }
  50% { box-shadow: 0 0 30px rgba(212, 165, 116, 0.8), inset 0 0 20px rgba(212, 165, 116, 0.3); }
}

.avatar-glow {
  position: absolute;
  top: -10px;
  left: -10px;
  width: calc(100% + 20px);
  height: calc(100% + 20px);
  border-radius: 50%;
  background: radial-gradient(circle, rgba(212, 165, 116, 0.3) 0%, transparent 70%);
  pointer-events: none;
  animation: glow-rotate 4s linear infinite;
}

@keyframes glow-rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.user-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
  font-weight: bold;
  color: white;
  overflow: hidden;
  position: relative;
}

.user-info {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.user-name {
  font-size: 20px;
  font-weight: 700;
  color: var(--neutral-100);
  margin-bottom: 6px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.verification-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.verification-tag.verified {
  background: rgba(144, 169, 85, 0.2);
  color: #90a955;
  border: 1px solid rgba(144, 169, 85, 0.4);
}

.verification-tag.verified i {
  color: #90a955;
  font-size: 10px;
}

.verification-tag.unverified {
  background: rgba(230, 57, 70, 0.2);
  color: #e63946;
  border: 1px solid rgba(230, 57, 70, 0.4);
}

.verification-tag.unverified i {
  color: #e63946;
  font-size: 10px;
}

.user-points {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  padding: 6px 12px;
  background: rgba(212, 165, 116, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(212, 165, 116, 0.2);
  transition: all 0.3s ease;
}

.user-points:hover {
  background: rgba(212, 165, 116, 0.15);
  border-color: rgba(212, 165, 116, 0.3);
  transform: translateX(2px);
}

.points-icon {
  color: var(--primary-color);
  font-size: 14px;
}

.points-text {
  font-size: 14px;
  color: var(--primary-color);
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.user-phone {
  font-size: 14px;
  color: var(--neutral-400);
  margin-bottom: 6px;
  font-family: 'Courier New', monospace;
}

.user-id {
  font-size: 12px;
  color: var(--neutral-400);
  margin-bottom: 12px;
}

.user-address {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.user-address:hover {
  background: rgba(212, 165, 116, 0.15);
  border-color: var(--primary-color);
  transform: translateX(2px);
}

.address-content {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.address-icon {
  color: var(--primary-color);
  font-size: 12px;
}

.address-text {
  font-size: 11px;
  color: var(--neutral-300);
  font-family: 'Courier New', monospace;
  flex: 1;
}

.address-text-warning {
  color: var(--warning-color);
}

.address-text-loading {
  color: var(--primary-color);
}

.address-label {
  font-size: 11px;
  color: var(--primary-color);
  font-weight: 600;
}

/* 会员中心卡片 */
.membership-card {
  background: linear-gradient(135deg, rgba(42, 42, 42, 0.95) 0%, rgba(46, 46, 46, 0.9) 100%);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 16px;
  margin: 16px 0;
  border: 1px solid rgba(212, 165, 116, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  transition: all 0.3s ease;
  cursor: pointer;
}

.membership-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6), 0 0 20px rgba(212, 165, 116, 0.2);
}

.membership-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.crown-icon {
  width: 40px;
  height: 40px;
  background: var(--gradient-primary);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  box-shadow: 0 4px 12px rgba(212, 165, 116, 0.3);
}

.membership-info {
  flex: 1;
  margin-left: 12px;
}

.membership-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--neutral-100);
}

.points-display {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--neutral-400);
  font-size: 12px;
}

.points-value {
  color: var(--primary-color);
  font-weight: 600;
}

/* 资产总览卡片 */
.assets-overview {
  background: linear-gradient(145deg, rgba(42, 42, 42, 0.95) 0%, rgba(46, 46, 46, 0.9) 100%);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 20px;
  margin: 16px 0;
  border: 1px solid rgba(212, 165, 116, 0.2);
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.5);
  position: relative;
  overflow: hidden;
}

.assets-overview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gradient-primary);
  box-shadow: 0 0 10px rgba(212, 165, 116, 0.5);
}

.assets-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.assets-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: var(--neutral-100);
}

.assets-eye {
  color: var(--primary-color);
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 4px;
  border-radius: 50%;
}

.assets-eye:hover {
  background: rgba(212, 165, 116, 0.2);
  transform: scale(1.1);
}

.assets-eye.eye-closed {
  color: var(--neutral-400);
}

.rewards-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: var(--neutral-400);
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 4px 8px;
  border-radius: 8px;
}

.rewards-info:hover {
  background: rgba(255, 255, 255, 0.05);
  color: var(--primary-color);
}

.rewards-count {
  color: var(--primary-color);
  font-weight: 600;
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.stat-item:hover {
  background: rgba(212, 165, 116, 0.1);
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

/* 可点击的统计项 */
.stat-item[onclick] {
  cursor: pointer;
}

.stat-value {
  font-size: 18px;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 4px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
  font-size: 12px;
  color: var(--neutral-400);
  font-weight: 500;
}

/* 快捷操作区域 */
.quick-actions {
  margin: 20px 0;
}

.action-grid {
  display: flex;
  flex-direction: row;
  gap: 16px;
  background: var(--gradient-card);
  border-radius: 16px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  overflow-x: auto;
  white-space: nowrap;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px 8px;
  background: none;
  border: none;
  color: var(--neutral-100);
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12px;
  min-width: 70px;
}

.action-item:hover {
  background: rgba(212, 165, 116, 0.1);
  transform: translateY(-2px);
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  box-shadow: 0 4px 12px rgba(212, 165, 116, 0.3);
  transition: all 0.3s ease;
}

.action-item:hover .action-icon {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(212, 165, 116, 0.4);
}

.action-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--neutral-200);
  text-align: center;
}

/* 资产管理 */
.menu-section {
  margin: 20px 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--neutral-200);
  margin-bottom: 12px;
  padding: 0 4px;
}

.menu-list {
  background: var(--gradient-card);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: none;
  border-left: none;
  border-right: none;
  border-top: none;
  color: var(--neutral-100);
  cursor: pointer;
  transition: all 0.3s;
  width: 100%;
  text-align: left;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
  transform: translateX(2px);
}

.menu-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #d4a574;
  margin-right: 12px;
  font-size: 18px;
  font-weight: bold;
}

.menu-content {
  flex: 1;
}

.menu-title {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 2px;
}

.menu-desc {
  font-size: 12px;
  color: var(--neutral-400);
}

.menu-arrow {
  color: var(--neutral-400);
  font-size: 14px;
}

.premium-banner {
  background: var(--gradient-primary);
  border-radius: 12px;
  padding: 16px;
  margin: 20px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.premium-icon {
  font-size: 28px;
  font-weight: bold;
  color: white;
}

.premium-content {
  flex: 1;
}

.premium-title {
  font-size: 16px;
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.premium-desc {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

.premium-btn {
  background: white;
  color: var(--primary-gold);
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.premium-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}



/* 点击效果 */
.menu-item:active,
.premium-btn:active {
  transform: scale(0.98);
}

/* 头像相关样式 */
.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.default-avatar {
  font-size: 36px;
  color: white;
}

/* Font Awesome图标样式修正 */
.menu-icon i,
.premium-icon i,
.logout-btn i,
.nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.menu-arrow i {
  font-size: 12px;
}

/* 底部导航图标调整 */
.nav-icon {
  font-size: 20px;
  font-weight: normal;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .user-profile.premium-style {
    padding: 20px;
    gap: 16px;
  }
  
  .user-avatar-container {
    width: 70px;
    height: 70px;
  }
  
  .user-name {
    font-size: 18px;
    gap: 6px;
  }
  
  .verification-tag {
    font-size: 10px;
    padding: 1px 6px;
  }
  
  .action-grid {
    gap: 0;
    padding: 3px;
  }
  
  .action-icon {
    width: 44px;
    height: 44px;
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .stats-row {
    gap: 12px;
  }
  
  .stat-item {
    padding: 12px 8px;
  }
  
  .stat-value {
    font-size: 16px;
  }
  
  .user-name {
    font-size: 16px;
    gap: 4px;
  }
  
  .verification-tag {
    font-size: 9px;
    padding: 1px 4px;
  }
  
  .action-grid {
    gap: 0px;
    padding: 3px;
  }
  
  .action-item {
    padding: 12px 4px;
  }
  
  .action-icon {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }
  
  .action-label {
    font-size: 11px;
  }
}

.asset-card-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-top: 20px;
}

.asset-card {
  background: #232323;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.18);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1.5px solid rgba(255,255,255,0.08);
  transition: box-shadow 0.2s, border-color 0.2s;
  cursor: pointer;
}
.asset-card:hover {
  box-shadow: 0 6px 18px rgba(212,165,116,0.18);
  border-color: #d4a574;
}
.asset-image-wrapper {
  width: 100%;
  height: 200px;
  aspect-ratio: 1.2/1;
  background: #181818;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.asset-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}
.asset-info {
  padding: 14px 16px 12px 16px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.asset-name {
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.asset-code {
  font-size: 13px;
  color: #d4a574;
  display: flex;
  align-items: center;
  gap: 6px;
}

@media (max-width: 768px) {
  .asset-card-list {
    gap: 12px;
    grid-template-columns: repeat(2, 1fr);
  }
  .asset-card {
    border-radius: 12px;
  }
  .asset-info {
    padding: 10px 10px 8px 10px;
  }
  .asset-name {
    font-size: 15px;
  }
  .asset-code {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .asset-card-list {
    gap: 10px;
    grid-template-columns: repeat(2, 1fr);
  }
  .asset-card {
    border-radius: 10px;
  }
}
</style> 