<template>
  <AppPageHeader :title="'实名认证'" @back="$router.back()" />

  <div class="verify-page" :style="{ paddingTop: '40px' }">
    <main class="main-content">


      <!-- 认证状态显示 -->
      <div v-if="verificationStatus.isVerified" class="status-card verified">
        <div class="status-icon">
          <i class="fas fa-check-circle"></i>
        </div>
        <div class="status-content">
          <div class="status-title">认证已通过</div>
          <div class="status-desc">您已完成实名认证</div>
          <div class="verify-info">
            <div class="info-item">
              <span class="label">姓名：</span>
              <span class="value">{{ verificationStatus.idName }}</span>
            </div>
            <div class="info-item">
              <span class="label">证件号：</span>
              <span class="value">{{ verificationStatus.idNo }}</span>
            </div>
            <div class="info-item">
              <span class="label">认证时间：</span>
              <span class="value">{{ formatDate(verificationStatus.statusDate) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 认证表单 -->
      <div v-else class="verify-form">
        <!-- 认证类型选择 -->
        <div class="form-section">
          <div class="section-title">认证类型</div>
          <div class="verify-type-tabs">
            <button 
              class="type-tab" 
              :class="{ active: verifyForm.identifyType === 'person' }"
              @click="switchVerifyType('person')"
            >
              <i class="fas fa-user"></i>
              个人认证
            </button>
            <button 
              class="type-tab" 
              :class="{ active: verifyForm.identifyType === 'enterprise' }"
              @click="switchVerifyType('enterprise')"
            >
              <i class="fas fa-building"></i>
              企业认证
            </button>
          </div>
          
          <!-- 证件类型选择 -->
          <div class="form-group" style="margin-top: 20px;">
            <label class="form-label">证件类型</label>
            <CustomSelect
              v-model="verifyForm.idType"
              :options="verifyForm.identifyType === 'person' ? personIdTypeOptions : idTypeOptions"
              placeholder="请选择证件类型"
              @change="onIdTypeChange"
            />
          </div>
        </div>

        <!-- 证件照片上传 -->
        <div class="form-section">
          <div class="section-title">
            {{ verifyForm.identifyType === 'person' ? '证件照片' : '营业执照' }}
          </div>
          <div class="upload-area">
            <div v-if="verifyForm.identifyType === 'person'" class="upload-item">
              <div class="upload-card" @click="uploadIdCard('front')">
                <div v-if="imagePreview.front" class="image-preview">
                  <img :src="imagePreview.front" alt="身份证正面" />
                  <div class="image-overlay">
                    <i class="fas fa-edit"></i>
                    <span>重新上传</span>
                  </div>
                </div>
                <div v-else class="upload-placeholder">
                  <i class="fas fa-camera"></i>
                  <div class="upload-text">身份证正面</div>
                </div>
              </div>
            </div>
            <div v-if="verifyForm.identifyType === 'person'" class="upload-item">
              <div class="upload-card" @click="uploadIdCard('back')">
                <div v-if="imagePreview.back" class="image-preview">
                  <img :src="imagePreview.back" alt="身份证反面" />
                  <div class="image-overlay">
                    <i class="fas fa-edit"></i>
                    <span>重新上传</span>
                  </div>
                </div>
                <div v-else class="upload-placeholder">
                  <i class="fas fa-camera"></i>
                  <div class="upload-text">身份证反面</div>
                </div>
              </div>
            </div>
            <div v-if="verifyForm.identifyType === 'enterprise'" class="upload-item full-width">
              <div class="upload-card" @click="uploadIdCard('business')">
                <div v-if="imagePreview.business" class="image-preview">
                  <img :src="imagePreview.business" alt="营业执照" />
                  <div class="image-overlay">
                    <i class="fas fa-edit"></i>
                    <span>重新上传</span>
                  </div>
                </div>
                <div v-else class="upload-placeholder">
                  <i class="fas fa-camera"></i>
                  <div class="upload-text">营业执照照片</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 个人认证表单 -->
        <div v-if="verifyForm.identifyType === 'person'" class="person-form">
          <div class="form-section">
            <div class="section-title">基本信息</div>
            
            <div class="form-group">
              <label class="form-label">真实姓名</label>
              <input 
                v-model="verifyForm.idName" 
                type="text" 
                class="form-input"
                placeholder="请输入您的真实姓名"
                maxlength="20"
                :readonly="fieldLocked.idName"
              />
            </div>

            <div class="form-group">
              <label class="form-label">证件号码</label>
              <input 
                v-model="verifyForm.idNo" 
                type="text" 
                class="form-input"
                placeholder="请输入证件号码"
                maxlength="30"
                :readonly="fieldLocked.idNo"
              />
            </div>

            <div class="form-group">
              <label class="form-label">证件到期时间</label>
              <div v-if="verifyForm.businessTerm === 'permanent' || verifyForm.businessTerm === '长期' || verifyForm.businessTerm === '长期有效'" class="permanent-expiry">
                <input 
                  value="长期有效" 
                  type="text" 
                  class="form-input permanent-input"
                  readonly
                />
                <button 
                  v-if="!fieldLocked.businessTerm"
                  class="change-expiry-btn"
                  @click="changeToDateInput"
                  type="button"
                >
                  改为日期
                </button>
              </div>
              <div v-else class="date-expiry">
                <input 
                  v-model="verifyForm.businessTerm" 
                  type="date" 
                  class="form-input"
                  placeholder="请选择证件到期时间"
                  :readonly="fieldLocked.businessTerm"
                />
                <button 
                  v-if="!fieldLocked.businessTerm"
                  class="change-expiry-btn"
                  @click="changeToPermanent"
                  type="button"
                >
                  设为长期
                </button>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">手机号码</label>
              <div class="phone-input-group">
                <input 
                  v-model="verifyForm.phone" 
                  type="tel" 
                  class="form-input"
                  placeholder="请输入手机号"
                  maxlength="11"
                  :readonly="fieldLocked.phone"
                />
                <button 
                  class="sms-btn" 
                  :disabled="smsCountdown > 0"
                  @click="sendSmsCode"
                >
                  {{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
                </button>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">验证码</label>
              <input 
                v-model="verifyForm.smsCode" 
                type="text" 
                class="form-input"
                placeholder="请输入短信验证码"
                maxlength="6"
              />
            </div>

            <div class="form-group">
              <label class="form-label">详细地址</label>
              <div class="address-group">
                <CustomSelect
                  v-model="verifyForm.province"
                  :options="provinceOptions"
                  placeholder="请选择省份"
                  class="address-select"
                  @change="onProvinceChange"
                />
                <CustomSelect
                  v-model="verifyForm.city"
                  :options="cityOptions"
                  placeholder="请选择城市"
                  class="address-select"
                  :disabled="!verifyForm.province"
                />
              </div>
              <input 
                v-model="verifyForm.address" 
                type="text" 
                class="form-input"
                placeholder="请输入详细地址"
                maxlength="100"
                :readonly="fieldLocked.address"
              />
            </div>
          </div>


        </div>

        <!-- 企业认证表单 -->
        <div v-else class="enterprise-form">
          <div class="form-section">
            <div class="section-title">企业信息</div>
            
            <div class="form-group">
              <label class="form-label">企业名称</label>
              <input 
                v-model="verifyForm.idName" 
                type="text" 
                class="form-input"
                placeholder="请输入企业全称"
                maxlength="50"
                :readonly="fieldLocked.idName"
              />
            </div>

            <div class="form-group">
              <label class="form-label">统一社会信用代码</label>
              <input 
                v-model="verifyForm.idNo" 
                type="text" 
                class="form-input"
                placeholder="请输入统一社会信用代码"
                maxlength="30"
                :readonly="fieldLocked.idNo"
              />
            </div>

            <div class="form-group">
              <label class="form-label">法人姓名</label>
              <input 
                v-model="verifyForm.legalName" 
                type="text" 
                class="form-input"
                placeholder="请输入法人姓名"
                maxlength="20"
                :readonly="fieldLocked.legalName"
              />
            </div>

            <div class="form-group">
              <label class="form-label">营业地址</label>
              <div class="address-group">
                <CustomSelect
                  v-model="verifyForm.enterpriseProvince"
                  :options="provinceOptions"
                  placeholder="省份"
                  class="address-select"
                  @change="onEnterpriseProvinceChange"
                />
                <CustomSelect
                  v-model="verifyForm.enterpriseCity"
                  :options="enterpriseCityOptions"
                  placeholder="城市"
                  class="address-select"
                  :disabled="!verifyForm.enterpriseProvince"
                  @change="onEnterpriseCityChange"
                />
                <CustomSelect
                  v-model="verifyForm.enterpriseDistrict"
                  :options="enterpriseDistrictOptions"
                  placeholder="区县"
                  class="address-select"
                  :disabled="!verifyForm.enterpriseCity"
                />
              </div>
              <input 
                v-model="verifyForm.registrationPlace" 
                type="text" 
                class="form-input"
                placeholder="请输入详细营业地址"
                maxlength="100"
                :readonly="fieldLocked.address"
                style="margin-top: 12px;"
              />
            </div>

            <div class="form-group">
              <label class="form-label">经营范围</label>
              <textarea 
                v-model="verifyForm.businessScope" 
                class="form-textarea"
                placeholder="请输入主营业务范围"
                maxlength="200"
                :readonly="fieldLocked.businessScope"
              ></textarea>
            </div>

            <div class="form-group">
              <label class="form-label">营业期限</label>
              <div v-if="verifyForm.businessTerm === 'permanent' || verifyForm.businessTerm === '长期' || verifyForm.businessTerm === '长期有效'" class="permanent-expiry">
                <input 
                  value="长期有效" 
                  type="text" 
                  class="form-input permanent-input"
                  readonly
                />
                <button 
                  v-if="!fieldLocked.businessTerm"
                  class="change-expiry-btn"
                  @click="changeToDateInput"
                  type="button"
                >
                  改为日期
                </button>
              </div>
              <div v-else class="date-expiry">
                <input 
                  v-model="verifyForm.businessTerm" 
                  type="date" 
                  class="form-input"
                  placeholder="请选择营业期限到期时间"
                  :min="todayDate"
                  :readonly="fieldLocked.businessTerm"
                />
                <button 
                  v-if="!fieldLocked.businessTerm"
                  class="change-expiry-btn"
                  @click="changeToPermanent"
                  type="button"
                >
                  设为长期
                </button>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">手机号码</label>
              <div class="phone-input-group">
                <input 
                  v-model="verifyForm.phone" 
                  type="tel" 
                  class="form-input"
                  placeholder="请输入手机号"
                  maxlength="11"
                  :readonly="fieldLocked.phone"
                />
                <button 
                  class="sms-btn" 
                  :disabled="smsCountdown > 0"
                  @click="sendSmsCode"
                >
                  {{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
                </button>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">验证码</label>
              <input 
                v-model="verifyForm.smsCode" 
                type="text" 
                class="form-input"
                placeholder="请输入短信验证码"
                maxlength="6"
              />
            </div>
            
            <div class="form-group">
              <label class="form-label">企业邮箱（选填）</label>
              <input 
                v-model="verifyForm.enterpriseEmail" 
                type="email" 
                class="form-input"
                placeholder="请输入企业邮箱（选填）"
                maxlength="50"
              />
            </div>
          </div>


        </div>

        <!-- 提交按钮 -->
        <button 
          class="submit-btn" 
          :disabled="!canSubmit || isSubmitting"
          @click="submitVerification"
        >
          <i v-if="isSubmitting" class="fas fa-spinner fa-spin"></i>
          {{ isSubmitting ? '提交中...' : '提交认证' }}
        </button>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useNotification } from '@/composables/useNotification'
import UserAPI from '@/api/user'
import AuthAPI from '@/api/auth'
import DictionaryAPI from '@/api/dictionary'
import type { DictionaryItem } from '@/api/dictionary'
import { OcrAPI } from '@/api/ocr'
import type { IdCardFrontResult, IdCardBackResult, BusinessLicenseResult } from '@/api/ocr'
import UploadAPI from '@/api/upload'
import CustomSelect from '@/components/CustomSelect.vue'
import AppPageHeader from '@/components/AppPageHeader.vue'

// 响应式数据
const router = useRouter()
const { success, error, info } = useNotification()

// 认证状态
const verificationStatus = ref({
  isVerified: false,
  idName: '',
  idNo: '',
  statusDate: '',
  identifyType: 'person' as 'person' | 'enterprise'
})

// 表单数据
const verifyForm = reactive({
  identifyType: 'person' as 'person' | 'enterprise',
  idType: '0',
  idName: '',
  idNo: '',
  idCard: '', // 证件照片filePath
  businessTerm: '', // 证件到期时间（个人：证件有效期，企业：营业期限）
  phone: '',
  address: '',
  smsCode: '',
  legalName: '',
  registrationPlace: '',
  registrationArea: '',
  businessScope: '',
  enterprisePhone: '',
  enterpriseEmail: '',
  province: '',
  city: '',
  // 企业地址选择字段
  enterpriseProvince: '',
  enterpriseCity: '',
  enterpriseDistrict: ''
})

// 短信验证码倒计时
const smsCountdown = ref(0)
const isSubmitting = ref(false)

// 字典数据
const idTypeOptions = ref<DictionaryItem[]>([]) // 企业证件类型
const personIdTypeOptions = ref<DictionaryItem[]>([]) // 个人证件类型
const provinceOptions = ref<DictionaryItem[]>([])
const cityOptions = ref<DictionaryItem[]>([])
// 企业地址选择数据
const enterpriseCityOptions = ref<DictionaryItem[]>([])
const enterpriseDistrictOptions = ref<DictionaryItem[]>([])

// 企业级联地址选择（已改为分离的下拉选择）

// 今日日期（用于日期选择器的最小值限制）
const todayDate = computed(() => {
  const today = new Date()
  const year = today.getFullYear()
  const month = (today.getMonth() + 1).toString().padStart(2, '0')
  const day = today.getDate().toString().padStart(2, '0')
  return `${year}-${month}-${day}`
})

// 字段锁定状态
const fieldLocked = reactive({
  idName: false,        // 姓名/企业名称
  idNo: false,          // 身份证号/统一社会信用代码
  businessTerm: false,  // 证件到期时间/营业期限
  address: false,       // 地址
  phone: false,         // 手机号
  legalName: false,     // 法人姓名
  businessScope: false  // 经营范围
})

// 图片预览状态
const imagePreview = reactive({
  front: '', // 身份证正面预览URL
  back: '',  // 身份证背面预览URL
  business: '' // 营业执照预览URL
})

/**
 * 返回上一页
 */
const goBack = () => {
  router.back()
}

/**
 * 切换认证类型
 */
const switchVerifyType = (type: 'person' | 'enterprise') => {
  // 保存手机号码，切换类型时不应该丢失
  const currentPhone = verifyForm.phone
  const currentPhoneLocked = fieldLocked.phone
  
  verifyForm.identifyType = type
  // 重置表单数据
  Object.assign(verifyForm, {
    identifyType: type,
    idType: type === 'person' ? '0' : '',
    idName: '',
    idNo: '',
    idCard: '',
    businessTerm: '',
    phone: currentPhone, // 保持手机号码不变
    address: '',
    smsCode: '',
    legalName: '',
    registrationPlace: '',
    registrationArea: '',
    businessScope: '',
    enterprisePhone: '',
    enterpriseEmail: '',
    province: '',
    city: '',
    // 重置企业地址选择字段
    enterpriseProvince: '',
    enterpriseCity: '',
    enterpriseDistrict: ''
  })
  
  // 保持手机号码锁定状态
  fieldLocked.phone = currentPhoneLocked
  
  // 重置企业地址选择数据
  if (type === 'enterprise') {
    enterpriseCityOptions.value = []
    enterpriseDistrictOptions.value = []
  }
}

/**
 * 证件类型变化处理
 */
const onIdTypeChange = (value: string, option: DictionaryItem | null) => {
  const typeText = verifyForm.identifyType === 'person' ? '个人' : '企业'
  console.log(`${typeText}证件类型变化:`, value, option?.label)
  // 这里可以根据证件类型的变化来调整表单验证规则或其他逻辑
}

/**
 * 省份变化处理
 */
const onProvinceChange = async (value: string, option: DictionaryItem | null) => {
  console.log('省份变化:', value, option?.label)
  
  // 清空城市选择
  verifyForm.city = ''
  cityOptions.value = []
  
  if (value) {
    try {
      // 加载对应省份的城市数据
      const response = await DictionaryAPI.getCitiesByProvinceId(value)
      if (response.code === 200 && response.data) {
        cityOptions.value = response.data
        console.log(`加载${option?.label}的城市数据:`, response.data.length, '个城市')
      }
    } catch (err) {
      console.warn('加载城市数据失败:', err)
      // 加载失败时提供四川省的默认城市数据
      if (value === '510000') {
        cityOptions.value = [
          { value: '510100', label: '成都市', code: '510100', type: 'city', sort: 1, isDefault: false, status: 'active' },
          { value: '510700', label: '绵阳市', code: '510700', type: 'city', sort: 2, isDefault: false, status: 'active' }
        ]
      }
    }
  }
}

/**
 * 企业省份变化处理
 */
const onEnterpriseProvinceChange = async (value: string, option: DictionaryItem | null) => {
  console.log('企业省份变化:', value, option?.label)
  
  // 清空城市和区县选择
  verifyForm.enterpriseCity = ''
  verifyForm.enterpriseDistrict = ''
  enterpriseCityOptions.value = []
  enterpriseDistrictOptions.value = []
  
  if (value) {
    try {
      // 加载对应省份的城市数据
      const response = await DictionaryAPI.getCitiesByProvinceId(value)
      if (response.code === 200 && response.data) {
        enterpriseCityOptions.value = response.data
        console.log(`加载企业${option?.label}的城市数据:`, response.data.length, '个城市')
      }
    } catch (err) {
      console.warn('加载企业城市数据失败:', err)
      // 加载失败时提供四川省的默认城市数据
      if (value === '510000') {
        enterpriseCityOptions.value = [
          { value: '510100', label: '成都市', code: '510100', type: 'city', sort: 1, isDefault: false, status: 'active' },
          { value: '510700', label: '绵阳市', code: '510700', type: 'city', sort: 2, isDefault: false, status: 'active' }
        ]
      }
    }
  }
}

/**
 * 企业城市变化处理
 */
const onEnterpriseCityChange = async (value: string, option: DictionaryItem | null) => {
  console.log('企业城市变化:', value, option?.label)
  
  // 清空区县选择
  verifyForm.enterpriseDistrict = ''
  enterpriseDistrictOptions.value = []
  
  if (value) {
    try {
      // 加载对应城市的区县数据
      const response = await DictionaryAPI.getDistrictsByCityId(value)
      if (response.code === 200 && response.data) {
        enterpriseDistrictOptions.value = response.data
        console.log(`加载企业${option?.label}的区县数据:`, response.data.length, '个区县')
      }
    } catch (err) {
      console.warn('加载企业区县数据失败:', err)
      // 加载失败时提供成都市的默认区县数据
      if (value === '510100') {
        enterpriseDistrictOptions.value = [
          { value: '510104', label: '锦江区', code: '510104', type: 'district', sort: 1, isDefault: false, status: 'active' },
          { value: '510105', label: '青羊区', code: '510105', type: 'district', sort: 2, isDefault: false, status: 'active' },
          { value: '510106', label: '金牛区', code: '510106', type: 'district', sort: 3, isDefault: false, status: 'active' },
          { value: '510107', label: '武侯区', code: '510107', type: 'district', sort: 4, isDefault: false, status: 'active' },
          { value: '510108', label: '成华区', code: '510108', type: 'district', sort: 5, isDefault: false, status: 'active' }
        ]
      }
    }
  }
}

// 企业地址级联选择变化处理函数已移除，现在使用分离的下拉选择

/**
 * 发送短信验证码
 */
const sendSmsCode = async () => {
  if (!verifyForm.phone) {
    error('请输入手机号')
    return
  }

  if (!/^1[3-9]\d{9}$/.test(verifyForm.phone)) {
    error('请输入正确的手机号')
    return
  }

  try {
    await AuthAPI.sendSmsCode({
      phone: verifyForm.phone,
      scene: 'identify'
    })
    
    success(`验证码已发送到手机号 ${verifyForm.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')}，请注意查收！`)
    
    // 开始倒计时
    smsCountdown.value = 60
    const timer = setInterval(() => {
      smsCountdown.value--
      if (smsCountdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
  } catch (err: any) {
    error(err.response?.data?.msg || '验证码发送失败')
  }
}

/**
 * 上传证件照片并进行OCR识别
 */
const uploadIdCard = async (type: 'front' | 'back' | 'business') => {
  // 创建文件输入元素
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'image/*'
  input.onchange = async (e) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (!file) return

    try {
      // 1. 验证文件：文件名长度、文件类型、文件大小
      const validation = UploadAPI.validateFile(file)
      if (!validation.valid) {
        error(validation.error || '文件验证失败')
        return
      }

      // 2. 创建图片预览
      const previewUrl = UploadAPI.createImagePreview(file)
      
      // 清理之前的预览URL
      if (imagePreview[type]) {
        UploadAPI.revokeImagePreview(imagePreview[type])
      }
      
      // 设置新的预览URL
      imagePreview[type] = previewUrl

      // 显示识别中状态
      const typeText = type === 'front' ? '身份证正面' : type === 'back' ? '身份证反面' : '营业执照'
      info(`正在识别${typeText}，请稍候...`)

      // 3. 将文件转换为base64
      const base64 = await OcrAPI.fileToBase64(file)
      
      // 4. 调用OCR识别接口
      const ocrParams: any = {
        idType: verifyForm.idType,
        base64: base64
      }
      
      // 为身份证添加side参数
      if (verifyForm.identifyType === 'person' && verifyForm.idType === '0') {
        ocrParams.side = type === 'front' ? 'face' : 'back'
      }

      console.log('OCR识别参数:', ocrParams)
      
      const ocrResponse = await OcrAPI.recognizeDocument(ocrParams)
      
      if (ocrResponse.code === 200 && ocrResponse.data) {
        // 5. 解析OCR结果并回填表单
        await fillFormWithOcrResult(ocrResponse.data, type)
        
        // 6. 上传文件到服务器
        info(`${typeText}识别成功，正在上传文件...`)
        
        try {
          const uploadResponse = await UploadAPI.uploadIdentifyFile(file, `${typeText}照片`)
          
          if (uploadResponse.code === 200 && uploadResponse.data) {
            // 保存文件路径到表单（后端需要的是filePath字段）
            const filePath = uploadResponse.data.filePath
            if (verifyForm.idCard) {
              verifyForm.idCard += ',' + filePath
            } else {
              verifyForm.idCard = filePath
            }
            
            success(`${typeText}识别成功并已上传，已自动填写相关信息`)
            console.log('文件上传成功:', uploadResponse.data)
          } else {
            throw new Error(uploadResponse.msg || '文件上传失败')
          }
        } catch (uploadErr: any) {
          console.error('文件上传失败:', uploadErr)
          error(`文件上传失败: ${uploadErr.message || '请重试'}`)
          
          // 即使上传失败，也保留OCR识别的结果
          // 使用临时的文件标识
          const tempFileId = `temp-${Date.now()}-${type}`
          if (verifyForm.idCard) {
            verifyForm.idCard += ',' + tempFileId
          } else {
            verifyForm.idCard = tempFileId
          }
        }
      } else {
        throw new Error(ocrResponse.msg || 'OCR识别失败')
      }
    } catch (err: any) {
      console.error('OCR识别失败:', err)
      error(`OCR识别失败: ${err.message || '请重试'}`)
    }
  }
  input.click()
}

/**
 * 根据OCR结果填充表单
 */
const fillFormWithOcrResult = async (ocrData: string, type: 'front' | 'back' | 'business') => {
  try {
    if (type === 'front') {
      // 身份证正面
      const result = OcrAPI.parseIdCardFront(ocrData)
      if (result) {
        verifyForm.idName = result.name
        verifyForm.idNo = result.idNumber
        verifyForm.address = result.address
        
        // 锁定已填充的字段
        fieldLocked.idName = true
        fieldLocked.idNo = true
        fieldLocked.address = true
        
        console.log('身份证正面识别结果:', result)
      }
    } else if (type === 'back') {
      // 身份证背面
      const result = OcrAPI.parseIdCardBack(ocrData)
      if (result) {
        // 从身份证背面获取证件到期时间
        if (result.validTo) {
          verifyForm.businessTerm = result.validTo
          fieldLocked.businessTerm = true
          
          // 根据不同类型显示不同提示
          if (result.validTo === 'permanent') {
            success('识别到证件长期有效')
          } else {
            success(`识别到证件到期时间：${result.validTo}`)
          }
          console.log('身份证背面识别到期时间:', result.validTo)
        } else {
          console.log('身份证背面未识别到有效期限信息')
        }
        console.log('身份证背面识别结果:', result)
      } else {
        console.log('身份证背面识别失败')
      }
    } else if (type === 'business') {
      // 营业执照
      const result = OcrAPI.parseBusinessLicense(ocrData)
      if (result) {
        verifyForm.idName = result.companyName
        verifyForm.idNo = result.creditCode
        verifyForm.legalName = result.legalRepresentative
        // 将识别的地址填入营业地址字段
        verifyForm.registrationPlace = result.address
        verifyForm.businessScope = result.businessScope
        // 营业期限不再自动回填，需要用户手动输入
        // verifyForm.businessTerm = result.businessTerm
        
        // 锁定已填充的字段
        fieldLocked.idName = true
        fieldLocked.idNo = true
        fieldLocked.legalName = true
        fieldLocked.address = true  // 锁定营业地址字段
        fieldLocked.businessScope = true
        // 营业期限不锁定，允许用户手动输入
        // fieldLocked.businessTerm = true
        
        // 注册资本可能需要格式化
        if (result.registeredCapital) {
          // 这里可以进一步处理注册资本格式
        }
        
        console.log('营业执照识别结果:', result)
        info('营业执照识别成功，营业期限请手动输入')
      }
    }
  } catch (errorMsg: any) {
    console.error('填充表单失败:', errorMsg)
    error('表单填充失败，请手动输入信息')
  }
}

/**
 * 检查是否可以提交
 */
const canSubmit = computed(() => {
  const basicValid = verifyForm.idName && 
                    verifyForm.idNo && 
                    verifyForm.phone && 
                    verifyForm.smsCode &&
                    verifyForm.idCard

  if (verifyForm.identifyType === 'person') {
    return basicValid && verifyForm.address
  } else {
    return basicValid && 
           verifyForm.legalName && 
           verifyForm.registrationPlace && 
           verifyForm.businessScope &&
           verifyForm.businessTerm // 营业期限也需要填写
           // 企业认证使用统一的phone字段，不需要单独的enterprisePhone
           // 移除企业邮箱的必填校验，因为是选填
  }
})

/**
 * 格式化证件到期时间
 */
const formatBusinessTerm = (term: string) => {
  if (!term) return ''
  
  // 如果是"长期"或"permanent"，返回"长期"
  if (term === 'permanent' || term === '长期' || term === '长期有效') {
    return '长期'
  }
  
  // 如果是日期格式，转换为yyyy年MM月dd日格式
  if (term.includes('-')) {
    try {
      const date = new Date(term)
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}年${month}月${day}日`
    } catch (err) {
      console.warn('日期格式转换失败:', err)
      return term
    }
  }
  
  // 如果已经是yyyy年MM月dd日格式，直接返回
  if (term.match(/^\d{4}年\d{2}月\d{2}日$/)) {
    return term
  }
  
  return term
}

/**
 * 获取省份和城市的中文名称
 */
const getLocationNames = () => {
  const provinceName = provinceOptions.value.find(p => p.value === verifyForm.province)?.label || ''
  const cityName = cityOptions.value.find(c => c.value === verifyForm.city)?.label || ''
  return { provinceName, cityName }
}

/**
 * 获取企业地址的中文名称
 */
const getEnterpriseLocationNames = () => {
  const provinceName = provinceOptions.value.find(p => p.value === verifyForm.enterpriseProvince)?.label || ''
  const cityName = enterpriseCityOptions.value.find(c => c.value === verifyForm.enterpriseCity)?.label || ''
  const districtName = enterpriseDistrictOptions.value.find(d => d.value === verifyForm.enterpriseDistrict)?.label || ''
  return { provinceName, cityName, districtName }
}

/**
 * 提交认证
 */
const submitVerification = async () => {
  if (!canSubmit.value) {
    error('请完善所有必填信息')
    return
  }

  isSubmitting.value = true

  try {
    // 格式化证件到期时间
    const formattedBusinessTerm = formatBusinessTerm(verifyForm.businessTerm)
    
    let submitData
    
    if (verifyForm.identifyType === 'person') {
      // 个人认证：获取省份和城市的中文名称
      const { provinceName, cityName } = getLocationNames()
      
      submitData = {
        ...verifyForm,
        province: provinceName,  // 传递中文名称而非编码
        city: cityName,          // 传递中文名称而非编码
        businessTerm: formattedBusinessTerm  // 格式化后的证件到期时间
      }
    } else {
      // 企业认证：构建registrationArea为省市区编码的逗号拼接，address取registrationPlace的值
      const registrationAreaCodes = []
      if (verifyForm.enterpriseProvince) registrationAreaCodes.push(verifyForm.enterpriseProvince)
      if (verifyForm.enterpriseCity) registrationAreaCodes.push(verifyForm.enterpriseCity)
      if (verifyForm.enterpriseDistrict) registrationAreaCodes.push(verifyForm.enterpriseDistrict)
      
      // 获取企业省市的中文名称
      const { provinceName, cityName } = getEnterpriseLocationNames()
      
      submitData = {
        ...verifyForm,
        province: verifyForm.enterpriseProvince,           // province字段使用enterpriseProvince的值
        city: verifyForm.enterpriseCity,                   // city字段使用enterpriseCity的值
        registrationArea: registrationAreaCodes.join(','), // 省市区编码用逗号拼接
        address: verifyForm.registrationPlace || '',       // address字段使用registrationPlace的值
        businessTerm: formattedBusinessTerm                 // 格式化后的营业期限
      }
    }
    
    console.log('提交实名认证数据:', submitData)
    
    await UserAPI.submitVerification(submitData)
    
    success('实名认证提交成功，请等待审核')
    
    // 重新加载认证状态
    setTimeout(() => {
      loadVerificationStatus()
    }, 1000)
    
  } catch (err: any) {
    error(err.response?.data?.msg || '提交失败，请重试')
  } finally {
    isSubmitting.value = false
  }
}

/**
 * 格式化日期
 */
const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

/**
 * 将证件到期时间改为日期输入
 */
const changeToDateInput = () => {
  verifyForm.businessTerm = ''
  fieldLocked.businessTerm = false
}

/**
 * 将证件到期时间设为长期有效
 */
const changeToPermanent = () => {
  verifyForm.businessTerm = 'permanent'
}

/**
 * 加载字典数据
 */
const loadDictionaryData = async () => {
  try {
    // 并行加载所有字典数据
    const [idTypeResponse, personIdTypeResponse, provinceResponse] = await Promise.all([
      DictionaryAPI.getIdTypes(),
      DictionaryAPI.getPersonIdTypes(),
      DictionaryAPI.getProvinces()
    ])
    
    // 加载企业证件类型
    if (idTypeResponse.code === 200 && idTypeResponse.data) {
      idTypeOptions.value = idTypeResponse.data
    }
    
    // 加载个人证件类型
    if (personIdTypeResponse.code === 200 && personIdTypeResponse.data) {
      personIdTypeOptions.value = personIdTypeResponse.data
    }
    
    // 加载省份数据
    if (provinceResponse.code === 200 && provinceResponse.data) {
      provinceOptions.value = provinceResponse.data
      console.log('省份数据加载成功:', provinceResponse.data.length, '个省份')
    }
  } catch (err) {
    console.warn('Failed to load dictionary data:', err)
    // 设置默认值
    idTypeOptions.value = [
      { 
        value: '1', 
        label: '统一社会信用代码',
        code: '1',
        type: 'id_type',
        sort: 1,
        isDefault: true,
        status: 'active'
      },
      { 
        value: '2', 
        label: '营业执照注册号',
        code: '2',
        type: 'id_type',
        sort: 2,
        isDefault: false,
        status: 'active'
      },
      { 
        value: '3', 
        label: '组织机构代码',
        code: '3',
        type: 'id_type',
        sort: 3,
        isDefault: false,
        status: 'active'
      }
    ]
    
    // 设置个人证件类型默认值
    personIdTypeOptions.value = [
      { 
        value: '0', 
        label: '身份证', 
        code: '0', 
        type: 'id_type_person', 
        sort: 1, 
        isDefault: true, 
        status: 'active' 
      },
      { 
        value: '1', 
        label: '护照', 
        code: '1', 
        type: 'id_type_person', 
        sort: 2, 
        isDefault: false, 
        status: 'active' 
      },
      { 
        value: '2', 
        label: '军官证', 
        code: '2', 
        type: 'id_type_person', 
        sort: 3, 
        isDefault: false, 
        status: 'active' 
      }
    ]
    
    // 设置省份默认值
    provinceOptions.value = [
      { 
        value: '510000', 
        label: '四川省', 
        code: '510000', 
        type: 'province', 
        sort: 1, 
        isDefault: true, 
        status: 'active' 
      },
      { 
        value: '110000', 
        label: '北京市', 
        code: '110000', 
        type: 'province', 
        sort: 2, 
        isDefault: false, 
        status: 'active' 
      },
      { 
        value: '310000', 
        label: '上海市', 
        code: '310000', 
        type: 'province', 
        sort: 3, 
        isDefault: false, 
        status: 'active' 
      }
    ]
  }
}

/**
 * 加载用户信息并填入手机号
 */
const loadUserInfo = async () => {
  try {
    // 首先尝试从localStorage获取用户信息
    const userInfo = localStorage.getItem('userInfo')
    if (userInfo) {
      const user = JSON.parse(userInfo)
      if (user.phone || user.phonenumber) {
        verifyForm.phone = user.phone || user.phonenumber
        fieldLocked.phone = true // 锁定手机号字段
        return
      }
    }

    // 如果localStorage中没有手机号，调用API获取
    const response = await AuthAPI.getUserInfo()
    if (response.code === 200 && response.user) {
      const userData = response.user
      if (userData.phonenumber) {
        verifyForm.phone = userData.phonenumber
        fieldLocked.phone = true // 锁定手机号字段
        // 同时更新localStorage中的用户信息，统一使用phone字段
        const userToSave = { ...userData, phone: userData.phonenumber }
        localStorage.setItem('userInfo', JSON.stringify(userToSave))
      }
    }
  } catch (err) {
    console.warn('获取用户信息失败:', err)
    // 获取失败不影响其他功能，用户可以手动输入手机号
  }
}

/**
 * 加载认证状态
 */
const loadVerificationStatus = async () => {
  try {
    const response = await UserAPI.getVerificationStatus()
    if (response.code === 200 && response.data) {
      const data = response.data
      verificationStatus.value = {
        isVerified: data.statusCd === '1000',
        idName: data.idName || '',
        idNo: data.idNo || '',
        statusDate: data.statusDate || '',
        identifyType: data.identifyType || 'person'
      }
    }
  } catch (err) {
    // 如果获取失败，说明未认证，保持默认状态
    console.warn('Failed to load verification status:', err)
  }
}

// 生命周期钩子
onMounted(async () => {
  // 并行加载各种数据
  await Promise.all([
    loadVerificationStatus(),
    loadDictionaryData(),
    loadUserInfo() // 添加用户信息加载
  ])
})

// 组件卸载时清理图片预览URL
onUnmounted(() => {
  // 释放所有图片预览URL
  Object.values(imagePreview).forEach(url => {
    if (url) {
      UploadAPI.revokeImagePreview(url)
    }
  })
})
</script>

<style scoped>
.verify-page {
  background: var(--gradient-hero);
  color: var(--neutral-100);
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.main-content {
  padding: 0 16px 40px 16px;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  position: sticky;
  top: 0;
  background: var(--gradient-hero);
  z-index: 10;
  position: relative;
}

.back-btn {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--neutral-100);
  font-size: 18px;
  padding: 8px;
  cursor: pointer;
  border-radius: 8px;
  transition: background-color 0.3s;
  z-index: 1;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  text-align: center;
}

.status-card {
  background: var(--gradient-card);
  border-radius: 16px;
  padding: 24px;
  margin: 20px 0;
  border: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.status-card.verified {
  border-color: rgba(52, 211, 153, 0.3);
  background: linear-gradient(135deg, rgba(52, 211, 153, 0.1), rgba(16, 185, 129, 0.05));
}

.status-icon {
  font-size: 48px;
  color: #10B981;
  margin-bottom: 16px;
}

.status-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--neutral-100);
}

.status-desc {
  font-size: 14px;
  color: var(--neutral-400);
  margin-bottom: 20px;
}

.verify-info {
  text-align: left;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  color: var(--neutral-400);
  font-size: 14px;
}

.value {
  color: var(--neutral-100);
  font-size: 14px;
  font-weight: 500;
}

.verify-form {
  margin: 20px 0;
}

.form-section {
  background: var(--gradient-card);
  border-radius: 12px;
  padding: 20px;
  margin: 16px 0;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--neutral-100);
  margin-bottom: 16px;
}

.verify-type-tabs {
  display: flex;
  gap: 12px;
}

.type-tab {
  flex: 1;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
  color: var(--neutral-400);
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.type-tab.active {
  background: var(--gradient-primary);
  border-color: var(--primary-gold);
  color: white;
}

.type-tab i {
  font-size: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  color: var(--neutral-300);
  margin-bottom: 8px;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 14px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: var(--neutral-100);
  font-size: 16px;
  transition: all 0.3s;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-gold);
  box-shadow: 0 0 0 3px rgba(200, 134, 13, 0.2);
}

.form-input::placeholder,
.form-select::placeholder,
.form-textarea::placeholder {
  color: var(--neutral-500);
}

/* 只读字段样式 */
.form-input:read-only,
.form-select:read-only,
.form-textarea:read-only {
  background: rgba(200, 134, 13, 0.1);
  border-color: rgba(200, 134, 13, 0.3);
  color: var(--primary-gold);
  cursor: not-allowed;
}

.form-input:read-only::placeholder,
.form-select:read-only::placeholder,
.form-textarea:read-only::placeholder {
  color: rgba(200, 134, 13, 0.5);
}

.phone-input-group {
  display: flex;
  gap: 12px;
}

.phone-input-group .form-input {
  flex: 1;
}

.sms-btn {
  background: var(--primary-gold);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  white-space: nowrap;
}

.sms-btn:disabled {
  background: rgba(255, 255, 255, 0.2);
  cursor: not-allowed;
}

.sms-btn:not(:disabled):hover {
  background: #B8860B;
}

.address-group {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.address-select {
  flex: 1;
  min-width: 0; /* 允许flex项目缩小到内容宽度以下 */
}

/* 地址选择器样式适配 */
.address-group .custom-select {
  flex: 1;
  min-width: 0; /* 确保可以缩小并显示省略号 */
}

.upload-area {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.upload-item {
  flex: 1;
  min-width: 120px;
}

.upload-item.full-width {
  flex: none;
  width: 100%;
}

.upload-card {
  background: rgba(255, 255, 255, 0.05);
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-card:hover {
  border-color: var(--primary-gold);
  background: rgba(255, 255, 255, 0.08);
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.upload-card i {
  font-size: 32px;
  color: var(--neutral-400);
  margin-bottom: 12px;
}

.upload-text {
  font-size: 14px;
  color: var(--neutral-400);
}

.image-preview {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-preview img {
  max-width: 100%;
  max-height: 120px;
  border-radius: 8px;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 8px;
  color: white;
  font-size: 14px;
}

.image-overlay i {
  font-size: 24px;
  margin-bottom: 8px;
  color: var(--primary-gold);
}

.upload-card:hover .image-overlay {
  opacity: 1;
}

.submit-btn {
  width: 100%;
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 16px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  margin-top: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.submit-btn:disabled {
  background: rgba(255, 255, 255, 0.2);
  cursor: not-allowed;
}

/* 证件到期时间相关样式 */
.permanent-expiry,
.date-expiry {
  display: flex;
  gap: 12px;
  align-items: center;
}

.permanent-input {
  flex: 1;
  background: rgba(52, 211, 153, 0.1) !important;
  border-color: rgba(52, 211, 153, 0.3) !important;
  color: #10B981 !important;
}

.change-expiry-btn {
  background: rgba(255, 255, 255, 0.1);
  color: var(--neutral-300);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
  white-space: nowrap;
  flex-shrink: 0;
}

.change-expiry-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: var(--primary-gold);
  color: var(--primary-gold);
}

.submit-btn:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(218, 165, 32, 0.3);
}

/* 响应式优化 - 地址选择器 */
@media (max-width: 640px) {
  .address-group {
    gap: 8px;
  }
  
  .address-select {
    min-width: 80px; /* 在移动端设置最小宽度 */
  }
}

@media (max-width: 480px) {
  .address-group {
    flex-direction: column;
    gap: 12px;
  }
  
  .address-select {
    min-width: unset;
  }
}
</style> 