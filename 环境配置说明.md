# 环境配置说明

## 概述
已成功配置项目支持不同环境的API接口地址切换。

## 配置文件

### 1. `.env` (默认配置)
```
VITE_API_BASE_URL=https://ts.sccdex.com/api
```

### 2. `.env.dev` (开发环境构建)
```
VITE_API_BASE_URL=https://ts.sccdex.com/api
```

### 3. `.env.ts` (测试环境)
```
VITE_API_BASE_URL=https://ts.sccdex.com/api
```

### 4. `.env.prod` (预生产环境)
```
VITE_API_BASE_URL=https://ts.sccdex.com/api
```

### 5. `.env.production` (生产环境)
```
VITE_API_BASE_URL=https://www.sccdex.com/api
```

### 6. `.env.local` (本地开发环境)
```
VITE_API_BASE_URL=http://127.0.0.1:9020
```

## 代码修改

### 1. `src/api/request.ts`
- 修改了BASE_URL的定义，使其从环境变量读取
- 原来的硬编码地址已被替换为：
```typescript
const BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://ts.sccdex.com/api'
```

### 2. `package.json`
- 添加了新的脚本命令：
```json
"dev:prod": "vite --mode production"
```

## 使用方法

### 开发服务器
```bash
# 默认开发环境（测试接口）
npm run dev
# 使用测试环境接口：https://ts.sccdex.com/api

# 生产环境模式（正式接口）
npm run dev:prod
# 使用生产环境接口：https://www.sccdex.com/api
```

### 构建命令
```bash
# 开发环境构建（测试接口）
npm run build:dev
# 使用测试环境接口：https://ts.sccdex.com/api

# 测试环境构建
npm run build:ts
# 使用测试环境接口：https://ts.sccdex.com/api

# 预生产环境构建
npm run build:prod
# 使用测试环境接口：https://ts.sccdex.com/api

# 生产环境构建
npm run build
# 使用生产环境接口：https://www.sccdex.com/api
```

## 验证方法
在浏览器控制台中可以看到当前环境信息：
- 当前环境模式
- API基础地址

## 注意事项
1. 环境变量必须以 `VITE_` 前缀开头才能在客户端代码中访问
2. 如果需要添加其他环境，可以创建对应的 `.env.{mode}` 文件
3. 环境变量文件的优先级：`.env.{mode}` > `.env`
