import request, { RequestError } from './request'

// 检查是否为认证错误（401）
function isAuthError(error: any): boolean {
  return error instanceof RequestError && (error.code === 401 || error.status === 401)
}

// 活动相关接口类型定义
export interface Activity {
  activityId: number;
  activityName: string;
  activityCover: string;
  description: string;
  startTime: string;
  endTime: string;
  drawTime: string;
  statusCd: string;
  createStaff?: string;
  createDate?: string;
  updateStaff?: string;
  updateDate?: string;
}

export interface ActivityPrize {
  id: number;
  name: string;
  type: string;
  quantity: number;
  // 可根据具体需求添加更多字段
}

export interface ActivityDetail extends Activity {
  prizeList: ActivityPrize[];
  isParticipated?: boolean; // 是否已经参与活动
  inviteCode?: string; // 邀请码
}

export interface ActivityListResponse {
  code: number;
  msg: string;
  rows: Activity[];
  total: number;
}

export interface ActivityDetailResponse {
  code: number;
  msg: string;
  data: ActivityDetail;
}

export interface ActivityJoinResponse {
  code: number;
  msg: string;
  data?: any;
}

export interface InviteRegisterRequest {
  username: string;
  password: string;
  confirmPassword: string;
  phone: string;
  smsCode: string;
  inviteCode: string;
}

export interface InviteRegisterResponse {
  code: number;
  msg: string;
  data?: any;
}

// 邀新排行榜相关类型定义
export interface InviteRankItem {
  userId: number;
  nickName: string;
  avatar: string;
  inviteNum: string;
}

export interface InviteRankResponse {
  code: number;
  msg: string;
  rows: InviteRankItem[];
  total: number;
}

// 中奖结果相关类型定义
export interface PrizeResultItem {
  couponCodeId: number;
  couponId: number;
  couponName: string;
  couponCover: string;
  description: string;
  couponCode: string;
  userId: number;
  isUsed: string;
  generateTime: string;
  useTime: string;
  startTime: string;
  endTime: string;
  statusCd: string;
}

export interface PrizeResultResponse {
  code: number;
  msg: string;
  data: PrizeResultItem[];
}

// 活动API类
class ActivityAPI {
  /**
   * 获取活动列表
   * @param params 查询参数
   * @returns 活动列表响应
   */
  static async getActivityList(params?: {
    pageNum?: number;
    pageSize?: number;
    activityName?: string;
    statusCd?: string;
  }): Promise<ActivityListResponse> {
    try {
      // 构建查询参数，直接传递给后端对象
      const queryParams = {
        pageNum: 1,
        pageSize: 20,
        ...params
      }

      // 直接传递queryParams作为第二个参数，而不是嵌套在对象中
      const response = await request.get('/activity/activity/client/list', queryParams) as ActivityListResponse
      return response
    } catch (error) {
      console.error('获取活动列表失败:', error)

      // 如果是认证错误（401），直接抛出，不返回假数据
      if (isAuthError(error)) {
        throw error
      }

      // 其他错误返回模拟数据作为降级方案
      return {
        code: 200,
        msg: '成功',
          rows: [
            {
              activityId: 1,
              activityName: '三星堆数字藏品展',
              activityCover: '/images/activity-1.jpg',
              description: '展示三星堆、金沙遗址等古蜀文明文物的数字藏品，感受千年文化的现代魅力',
              startTime: '2024-01-15 09:00:00',
              endTime: '2024-02-15 18:00:00',
              drawTime: '2024-02-16 10:00:00',
              statusCd: 'ONGOING'
            },
            {
              activityId: 2,
              activityName: '蜀锦织品藏品拍卖',
              activityCover: '/images/activity-2.jpg',
              description: '汇集传统蜀锦织品大师作品，展现千年织造工艺文化内涵',
              startTime: '2024-02-01 10:00:00',
              endTime: '2024-02-10 20:00:00',
              drawTime: '2024-02-11 14:00:00',
              statusCd: 'UPCOMING'
            },
            {
              activityId: 3,
              activityName: '新春数字藏品节',
              activityCover: '/images/activity-3.jpg',
              description: '庆祝新春佳节，限时推出古蜀文明主题数字藏品，与传统文化共度佳节',
              startTime: '2024-01-20 00:00:00',
              endTime: '2024-02-08 23:59:59',
              drawTime: '2024-02-09 12:00:00',
              statusCd: 'ONGOING'
            }
          ],
          total: 3

      }
    }
  }

  /**
   * 获取活动详情
   * @param activityId 活动ID
   * @returns 活动详情响应
   */
  static async getActivityDetail(activityId: number): Promise<ActivityDetailResponse> {
    try {
      const response = await request.get(`/activity/activity/client/${activityId}`) as ActivityDetailResponse
      return response
    } catch (error) {
      console.error('获取活动详情失败:', error)

      // 如果是认证错误（401），直接抛出，不返回假数据
      if (isAuthError(error)) {
        throw error
      }

      // 其他错误返回模拟数据作为降级方案
      return {
        code: 200,
        msg: '成功',
        data: {
          activityId,
          activityName: '三星堆数字藏品展',
          activityCover: '/images/activity-1.jpg',
          description: '展示三星堆、金沙遗址等古蜀文明文物的数字藏品，感受千年文化的现代魅力。本次活动将展出多件珍贵的古蜀文明数字藏品，每一件都承载着深厚的历史文化内涵。',
          startTime: '2024-01-15 09:00:00',
          endTime: '2024-02-15 18:00:00',
          drawTime: '2024-02-16 10:00:00',
          statusCd: 'ONGOING',
          createStaff: 'admin',
          createDate: '2024-01-10 10:00:00',
          updateStaff: 'admin',
          updateDate: '2024-01-12 15:30:00',
          prizeList: [
            {
              id: 1,
              name: '太阳神鸟金箔',
              type: '数字藏品',
              quantity: 100
            },
            {
              id: 2,
              name: '三星堆青铜面具',
              type: '数字藏品',
              quantity: 50
            },
            {
              id: 3,
              name: '金沙文化纪念徽章',
              type: '实物奖品',
              quantity: 20
            }
          ],
          isParticipated: false, // 默认未参与
          inviteCode: 'INVITE_' + activityId + '_' + Date.now() // 生成邀请码
        }
      }
    }
  }

  /**
   * 根据状态代码获取状态文本
   * @param statusCd 状态代码
   * @returns 状态文本
   */
  static getStatusText(statusCd: string): string {
    const statusMap: Record<string, string> = {
      'UPCOMING': '即将开始',
      'ONGOING': '进行中',
      'ENDED': '已结束',
      'CANCELLED': '已取消',
      'PAUSED': '已暂停'
    }
    return statusMap[statusCd] || '未知状态'
  }

  /**
   * 根据状态代码获取状态样式类
   * @param statusCd 状态代码
   * @returns 状态样式类
   */
  static getStatusClass(statusCd: string): string {
    const statusClassMap: Record<string, string> = {
      'UPCOMING': 'status-upcoming',
      'ONGOING': 'status-ongoing',
      'ENDED': 'status-ended',
      'CANCELLED': 'status-cancelled',
      'PAUSED': 'status-paused'
    }
    return statusClassMap[statusCd] || 'status-unknown'
  }

  /**
   * 判断活动是否进行中
   * @param activity 活动对象
   * @returns 是否进行中
   */
  static isActivityOngoing(activity: Activity): boolean {
    const now = new Date()
    const startTime = new Date(activity.startTime)
    const endTime = new Date(activity.endTime)
    return now >= startTime && now <= endTime && activity.statusCd === 'ONGOING'
  }

  /**
   * 判断活动是否即将开始
   * @param activity 活动对象
   * @returns 是否即将开始
   */
  static isActivityUpcoming(activity: Activity): boolean {
    const now = new Date()
    const startTime = new Date(activity.startTime)
    return now < startTime && activity.statusCd === 'UPCOMING'
  }

  /**
   * 参与活动
   * @param activityId 活动ID
   * @returns 参与结果响应
   */
  static async joinActivity(activityId: number): Promise<ActivityJoinResponse> {
    try {
      const response = await request.post('/activity/activity/client/join', activityId) as ActivityJoinResponse
      return response
    } catch (error) {
      console.error('参与活动失败:', error)
      throw error
    }
  }

  /**
   * 邀请注册
   * @param registerData 注册数据
   * @returns 注册结果响应
   */
  static async inviteRegister(registerData: InviteRegisterRequest): Promise<InviteRegisterResponse> {
    try {
      const response = await request.post('/activity/activity/client/register', registerData) as InviteRegisterResponse
      return response
    } catch (error) {
      console.error('邀请注册失败:', error)
      throw error
    }
  }

  /**
   * 获取邀新排行榜
   * @param activityId 活动ID
   * @param params 分页参数
   * @returns 排行榜响应
   */
  static async getInviteRank(activityId: number, params?: {
    pageNum?: number;
    pageSize?: number;
  }): Promise<InviteRankResponse> {
    try {
      // 构建查询参数
      const queryParams = {
        pageNum: 1,
        pageSize: 20,
        ...params
      }

      const response = await request.get(`/activity/activity/client/inviteRank/${activityId}`, queryParams) as InviteRankResponse
      return response
    } catch (error) {
      console.error('获取邀新排行榜失败:', error)

      // 如果是认证错误（401），直接抛出，不返回假数据
      if (isAuthError(error)) {
        throw error
      }

      // 其他错误返回模拟数据作为降级方案
      const mockData = [
        { userId: 1, nickName: '张三', avatar: '', inviteNum: '10' },
        { userId: 2, nickName: '李四', avatar: '', inviteNum: '8' },
        { userId: 3, nickName: '王五', avatar: '', inviteNum: '6' },
        { userId: 4, nickName: '赵六', avatar: '', inviteNum: '4' },
        { userId: 5, nickName: '钱七', avatar: '', inviteNum: '2' },
        { userId: 6, nickName: '孙八', avatar: '', inviteNum: '1' },
        { userId: 7, nickName: '周九', avatar: '', inviteNum: '1' },
        { userId: 8, nickName: '吴十', avatar: '', inviteNum: '1' },
      ]

      // 模拟分页逻辑
      const pageNum = params?.pageNum || 1
      const pageSize = params?.pageSize || 20
      const startIndex = (pageNum - 1) * pageSize
      const endIndex = startIndex + pageSize
      const paginatedData = mockData.slice(startIndex, endIndex)

      return {
        code: 200,
        msg: '成功',
        rows: paginatedData,
        total: mockData.length
      }
    }
  }

  /**
   * 获取中奖结果
   * @param activityId 活动ID
   * @returns 中奖结果响应
   */
  static async getPrizeResult(activityId: number): Promise<PrizeResultResponse> {
    try {
      const response = await request.get(`/activity/activity/client/prizeResult/${activityId}`) as PrizeResultResponse
      return response
    } catch (error) {
      console.error('获取中奖结果失败:', error)

      // 如果是认证错误（401），直接抛出，不返回假数据
      if (isAuthError(error)) {
        throw error
      }

      // 其他错误返回模拟数据作为降级方案
      return {
        code: 200,
        msg: '成功',
        data: [
          {
            couponCodeId: 1,
            couponId: 101,
            couponName: '太阳神鸟金箔数字藏品',
            couponCover: '/images/prize-1.jpg',
            description: '珍贵的古蜀文明数字藏品，承载千年文化内涵',
            couponCode: 'PRIZE_001_20240115',
            userId: 12345,
            isUsed: '0',
            generateTime: '2024-01-15 10:30:00',
            useTime: '',
            startTime: '2024-01-15 00:00:00',
            endTime: '2024-12-31 23:59:59',
            statusCd: 'VALID'
          },
          {
            couponCodeId: 2,
            couponId: 102,
            couponName: '三星堆青铜面具',
            couponCover: '/images/prize-2.jpg',
            description: '神秘的三星堆青铜面具数字藏品',
            couponCode: 'PRIZE_002_20240115',
            userId: 12345,
            isUsed: '1',
            generateTime: '2024-01-15 10:30:00',
            useTime: '2024-01-20 14:25:00',
            startTime: '2024-01-15 00:00:00',
            endTime: '2024-12-31 23:59:59',
            statusCd: 'USED'
          }
        ]
      }
    }
  }

  /**
   * 格式化时间显示
   * @param datetime 时间字符串
   * @returns 格式化后的时间
   */
  static formatDateTime(datetime: string): string {
    try {
      const date = new Date(datetime)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch (error) {
      return datetime
    }
  }
}

export default ActivityAPI
