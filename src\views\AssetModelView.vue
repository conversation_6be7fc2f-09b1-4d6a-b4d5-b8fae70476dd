<template>
  <div class="model-view-page">
    <!-- 顶部导航 -->
    <header class="model-header">
      <button class="back-btn" @click="goBack">
        <i class="fas fa-arrow-left"></i>
      </button>
      <div class="header-actions">
        <button class="action-btn" @click="showMenu">
          <i class="fas fa-ellipsis-h"></i>
        </button>
        <button class="action-btn record-btn" @click="startRecord">
          <i class="fas fa-video"></i>
        </button>
      </div>
    </header>

    <!-- 资产信息 -->
    <div class="asset-info-overlay">
      <h1 class="asset-title">{{ assetInfo.assetName || '加载中...' }}</h1>
      <div class="asset-details">
        <div class="asset-code">
          <i class="fas fa-certificate"></i>
          <span>{{ assetInfo.assetCode }}</span>
        </div>
        <div class="asset-time">
          <span>获得时间 {{ formatTime(assetInfo.chainTime || '') }}</span>
        </div>
      </div>
    </div>

    <!-- 3D模型展示区域 -->
    <div class="model-container" ref="modelContainer">
      <!-- 加载状态 -->
      <div v-if="loading" class="model-loading">
        <div class="loading-spinner">
          <i class="fas fa-spinner fa-spin"></i>
        </div>
        <p class="loading-text">正在加载3D模型...</p>
      </div>

      <!-- 错误状态 -->
      <div v-if="error" class="model-error">
        <div class="error-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <p class="error-text">{{ error }}</p>
        
        <!-- 错误详情和建议 -->
        <div class="error-details">
          <div class="error-suggestions">
            <h4>可能的解决方案：</h4>
                         <ul>
               <li v-if="error.includes('跨域') || error.includes('CORS')">
                 <i class="fas fa-shield-alt"></i>
                 <div>
                   <strong>CORS跨域问题：</strong>浏览器阻止了对外部域名的访问<br>
                   <small>需要服务器配置 Access-Control-Allow-Origin 响应头</small>
                 </div>
               </li>
               <li v-if="error.includes('跨域') || error.includes('CORS')">
                 <i class="fas fa-lightbulb"></i>
                 <div>
                   <strong>临时解决方案：</strong><br>
                   <small>1. 点击"演示模型"查看3D功能演示</small><br>
                   <small>2. 点击"尝试代理加载"使用不同的加载策略</small><br>
                   <small>3. 点击"下载后加载"尝试绕过CORS限制</small><br>
                   <small>4. 联系后端开发者配置CORS或提供代理接口</small>
                 </div>
               </li>
               <li v-if="error.includes('超时') || error.includes('timeout')">
                 <i class="fas fa-clock"></i>
                 <div>
                   <strong>请求超时问题：</strong><br>
                   <template v-if="error.includes('URL')">
                     <small>• 获取模型文件地址时超时，服务器响应较慢</small><br>
                   </template>
                   <template v-else>
                     <small>• 模型文件加载超时，可能是文件较大或网络较慢</small><br>
                   </template>
                   <small>• 建议检查网络连接，或点击"延长等待时间"重试</small>
                 </div>
               </li>
               <li v-if="error.includes('不存在') || error.includes('404')">
                 <i class="fas fa-file-excel"></i>
                 检查模型文件是否存在于服务器
               </li>
               <li v-if="!error.includes('跨域') && !error.includes('超时') && !error.includes('不存在')">
                 <i class="fas fa-tools"></i>
                 检查模型文件格式是否为标准GLB/GLTF
               </li>
             </ul>
          </div>
        </div>
        
                 <div class="error-actions">
           <button class="retry-btn" @click="loadModel">
             <i class="fas fa-redo"></i>
             重新加载
           </button>
           <button class="retry-btn" @click="retryWithLongerTimeout" v-if="error.includes('超时') || error.includes('timeout')">
             <i class="fas fa-hourglass-half"></i>
             延长等待时间
           </button>
           <button class="proxy-btn" @click="loadWithProxy" v-if="error.includes('跨域') || error.includes('CORS')">
             <i class="fas fa-globe"></i>
             尝试代理加载
           </button>
           <button class="download-btn" @click="downloadAndLoad" v-if="error.includes('跨域') || error.includes('CORS')">
             <i class="fas fa-download"></i>
             下载后加载
           </button>
           <button class="demo-btn" @click="loadDemoModel">
             <i class="fas fa-cube"></i>
             演示模型
           </button>
         </div>
      </div>

      <!-- Three.js 渲染容器 -->
      <div ref="threeContainer" class="three-container"></div>
    </div>

    <!-- 右侧功能按钮 -->
    <div class="side-actions">
      <button class="side-btn story-btn" @click="viewStory">
        <div class="btn-icon">
          <i class="fas fa-book-open"></i>
        </div>
        <span class="btn-label">藏品故事</span>
      </button>

      <button class="side-btn transfer-btn" @click="transferAsset">
        <div class="btn-icon">
          <i class="fas fa-gift"></i>
        </div>
        <span class="btn-label">转赠</span>
      </button>

      <button class="side-btn show-off-btn" @click="showOff">
        <div class="btn-icon">
          <i class="fas fa-star"></i>
        </div>
        <span class="btn-label">炫耀</span>
      </button>

      <button class="side-btn more-btn" @click="showMore">
        <div class="btn-icon">
          <i class="fas fa-ellipsis-h"></i>
        </div>
        <span class="btn-label">更多</span>
      </button>
    </div>

    <!-- 底部控制区域 -->
    <div class="bottom-controls">
      <div class="control-group">
        <button class="control-btn" @click="resetView" title="重置视角">
          <i class="fas fa-undo"></i>
        </button>
        <button class="control-btn" @click="toggleAutoRotate" title="自动旋转">
          <i class="fas fa-sync-alt" :class="{ active: autoRotate }"></i>
        </button>
        <button class="control-btn" @click="toggleWireframe" title="线框模式">
          <i class="fas fa-cube" :class="{ active: wireframe }"></i>
        </button>
        <button class="control-btn" @click="takeScreenshot" title="截图">
          <i class="fas fa-camera"></i>
        </button>
      </div>
    </div>

    <!-- 背景装饰元素 -->
    <div class="bg-decorations">
      <div class="decoration-item" style="--delay: 0s; --duration: 8s;"></div>
      <div class="decoration-item" style="--delay: 2s; --duration: 6s;"></div>
      <div class="decoration-item" style="--delay: 4s; --duration: 10s;"></div>
      <div class="decoration-item" style="--delay: 1s; --duration: 7s;"></div>
      <div class="decoration-item" style="--delay: 3s; --duration: 9s;"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useNotification } from '@/composables/useNotification'
import UserAPI, { type AssetData } from '@/api/user'
import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'

// 路由和通知
const route = useRoute()
const router = useRouter()
const { success, error: showError, info } = useNotification()

// 响应式数据
const loading = ref(true)
const error = ref('')
const modelContainer = ref<HTMLElement>()
const threeContainer = ref<HTMLElement>()
const autoRotate = ref(true)
const wireframe = ref(false)

// 资产信息
const assetInfo = ref<Partial<AssetData>>({
  assetName: '',
  assetCode: '',
  chainTime: '',
  assetId: 0
})

// Three.js 相关变量
let scene: THREE.Scene
let camera: THREE.PerspectiveCamera
let renderer: THREE.WebGLRenderer
let controls: OrbitControls
let model: THREE.Group
let animationMixer: THREE.AnimationMixer
let animationId: number

/**
 * 初始化Three.js场景
 */
const initThreeJS = () => {
  if (!threeContainer.value) return

  const container = threeContainer.value
  const width = container.clientWidth
  const height = container.clientHeight

  // 创建场景
  scene = new THREE.Scene()
  scene.background = null // 透明背景

  // 创建相机
  camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000)
  camera.position.set(0, 0, 5)

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({ 
    antialias: true, 
    alpha: true,
    powerPreference: "high-performance"
  })
  renderer.setSize(width, height)
  renderer.setPixelRatio(window.devicePixelRatio)
  renderer.shadowMap.enabled = true
  renderer.shadowMap.type = THREE.PCFSoftShadowMap
  renderer.toneMapping = THREE.ACESFilmicToneMapping
  renderer.toneMappingExposure = 1
  
  container.appendChild(renderer.domElement)

  // 创建控制器
  controls = new OrbitControls(camera, renderer.domElement)
  controls.enableDamping = true
  controls.dampingFactor = 0.05
  controls.enableZoom = true
  controls.enablePan = false
  controls.autoRotate = autoRotate.value
  controls.autoRotateSpeed = 2

  // 添加灯光
  setupLights()

  // 开始渲染循环
  animate()
}

/**
 * 设置灯光
 */
const setupLights = () => {
  // 环境光
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.6)
  scene.add(ambientLight)

  // 主光源
  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
  directionalLight.position.set(10, 10, 5)
  directionalLight.castShadow = true
  directionalLight.shadow.mapSize.width = 2048
  directionalLight.shadow.mapSize.height = 2048
  scene.add(directionalLight)

  // 填充光
  const fillLight = new THREE.DirectionalLight(0x4169ff, 0.3)
  fillLight.position.set(-5, 0, -5)
  scene.add(fillLight)

  // 点光源（增加立体感）
  const pointLight = new THREE.PointLight(0xff6b9d, 0.5, 100)
  pointLight.position.set(0, 5, 5)
  scene.add(pointLight)
}

/**
 * 检查网络连接
 */
const checkNetworkConnection = async (url: string): Promise<boolean> => {
  try {
    const response = await fetch(url, { 
      method: 'HEAD',
      mode: 'no-cors' // 避免CORS问题
    })
    return true
  } catch (err) {
    console.warn('Network check failed:', err)
    return false
  }
}

/**
 * 使用更长超时时间重试
 */
const retryWithLongerTimeout = async () => {
  try {
    loading.value = true
    error.value = ''
    
    const assetId = parseInt(route.params.assetId as string)
    if (!assetId) {
      throw new Error('资产ID不正确')
    }

    console.log('使用延长超时时间重新获取模型文件地址...')
    info('正在延长等待时间重新获取模型文件，请耐心等待...')
    
    // 使用更长的超时时间 (120秒)
    let modelResponse
    try {
      modelResponse = await UserAPI.getAssetModel(assetId)
    } catch (apiError: any) {
      if (apiError.message?.includes('超时')) {
        throw new Error('即使延长等待时间仍然超时，建议检查网络连接或联系客服')
      }
      throw new Error(`获取模型文件URL失败: ${apiError.message || '服务器错误'}`)
    }
    
    if (modelResponse.code !== 200 || !modelResponse.msg) {
      throw new Error('服务器返回的模型文件地址无效')
    }

    const modelUrl = modelResponse.msg
    console.log('延长超时后获取到模型文件URL:', modelUrl)
    
    // 继续正常的模型加载流程
    // 检查网络连接
    console.log('检查网络连接...')
    const isConnected = await checkNetworkConnection(modelUrl)
    if (!isConnected) {
      console.warn('网络连接检查失败，但继续尝试加载...')
    }

    // 尝试使用fetch预检查文件（延长超时版本）
    try {
      console.log('预检查模型文件（延长等待）...')
      const response = await fetch(modelUrl, { method: 'HEAD' })
      if (!response.ok) {
        console.warn(`文件预检查返回 ${response.status}，但继续尝试加载...`)
      } else {
        console.log('文件访问正常，内容类型:', response.headers.get('content-type'))
      }
    } catch (fetchErr: any) {
      console.warn('文件预检查失败:', fetchErr.message)
      
      // 判断是否为CORS相关错误
      const isCorsRelated = fetchErr.message.toLowerCase().includes('failed to fetch') ||
                           fetchErr.message.toLowerCase().includes('cors') ||
                           fetchErr.message.toLowerCase().includes('cross-origin') ||
                           fetchErr.message.toLowerCase().includes('network error')
      
      if (isCorsRelated) {
        console.warn('检测到CORS问题，跳过预检查继续尝试GLTFLoader...')
        // 对于CORS错误，我们不抛出异常，而是继续尝试GLTFLoader
      } else {
        // 对于其他类型的错误（如404、500等），直接抛出
        throw new Error(`无法访问模型文件: ${fetchErr.message}`)
      }
    }

    // 加载GLB模型
    console.log('开始加载GLB模型...')
    const loader = new GLTFLoader()
    
    // 设置加载超时
    const LOAD_TIMEOUT = 60000 // 60秒超时
    
    const gltf = await Promise.race([
      new Promise<any>((resolve, reject) => {
        loader.load(
          modelUrl,
          (loadedGltf) => {
            console.log('模型加载成功:', loadedGltf)
            resolve(loadedGltf)
          },
          (progress) => {
            console.log('Loading progress:', progress)
            if (progress.lengthComputable) {
              const percent = Math.round((progress.loaded / progress.total) * 100)
              console.log(`加载进度: ${percent}%`)
            }
          },
          (loadError) => {
            console.error('GLTFLoader错误:', loadError)
            reject(loadError)
          }
        )
      }),
      new Promise((_, reject) => {
        setTimeout(() => reject(new Error('模型加载超时')), LOAD_TIMEOUT)
      })
    ])

    console.log('开始处理模型场景...')
    await processLoadedModel(gltf)
    success('使用延长等待时间加载成功')
    console.log('延长超时重试完成！')
    
  } catch (err: any) {
    console.error('延长超时重试失败:', err)
    
    // 根据错误类型提供更具体的错误信息
    let errorMessage = '延长等待时间后仍然失败'
    
    if (err.message?.includes('超时')) {
      errorMessage = '即使延长等待时间仍然超时，建议稍后重试或联系客服'
    } else if (err.message?.includes('URL')) {
      errorMessage = '获取模型文件地址失败，请检查网络连接'
    } else if (err.message) {
      errorMessage = err.message
    }
    
    error.value = errorMessage
    loading.value = false
    showError(`延长超时重试失败: ${errorMessage}`)
  }
}

/**
 * 使用代理方式加载模型
 */
const loadWithProxy = async () => {
  try {
    loading.value = true
    error.value = ''
    
    const assetId = parseInt(route.params.assetId as string)
    const modelResponse = await UserAPI.getAssetModel(assetId)
    
    if (modelResponse.code !== 200 || !modelResponse.msg) {
      throw new Error('获取模型文件失败')
    }

    const originalUrl = modelResponse.msg
    
    // 尝试使用不同的加载策略
    const strategies = [
      // 策略1: 添加no-cors模式
      () => loadModelWithStrategy(originalUrl, { mode: 'no-cors' }),
      // 策略2: 使用代理前缀（如果后端支持）
      () => loadModelWithStrategy(`/api/proxy?url=${encodeURIComponent(originalUrl)}`),
      // 策略3: 尝试直接加载但忽略CORS
      () => loadModelDirectly(originalUrl)
    ]
    
    for (let i = 0; i < strategies.length; i++) {
      try {
        console.log(`尝试策略 ${i + 1}...`)
        await strategies[i]()
        success('使用代理方式加载成功')
        return
      } catch (err: any) {
        console.warn(`策略 ${i + 1} 失败:`, err.message)
        if (i === strategies.length - 1) {
          throw err
        }
      }
    }
    
  } catch (err: any) {
    console.error('代理加载失败:', err)
    error.value = `代理加载失败: ${err.message}`
    loading.value = false
    showError('所有加载策略都失败了')
  }
}

/**
 * 使用特定策略加载模型
 */
const loadModelWithStrategy = async (url: string, options?: RequestInit) => {
  const loader = new GLTFLoader()
  
  const gltf = await new Promise<any>((resolve, reject) => {
    loader.load(
      url,
      resolve,
      (progress) => {
        console.log('代理加载进度:', progress)
      },
      reject
    )
  })
  
  await processLoadedModel(gltf)
}

/**
 * 直接加载模型（忽略CORS）
 */
const loadModelDirectly = async (url: string) => {
  // 这里可以实现一些绕过CORS的技巧
  // 比如通过创建script标签或者其他方式
  console.log('尝试直接加载模型...')
  const loader = new GLTFLoader()
  
  const gltf = await new Promise<any>((resolve, reject) => {
    loader.load(url, resolve, undefined, reject)
  })
  
  await processLoadedModel(gltf)
}

/**
 * 下载文件后加载（绕过CORS）
 */
const downloadAndLoad = async () => {
  try {
    loading.value = true
    error.value = ''
    
    const assetId = parseInt(route.params.assetId as string)
    const modelResponse = await UserAPI.getAssetModel(assetId)
    
    if (modelResponse.code !== 200 || !modelResponse.msg) {
      throw new Error('获取模型文件失败')
    }

    const originalUrl = modelResponse.msg
    console.log('尝试下载模型文件:', originalUrl)
    
    // 显示下载进度
    info('正在下载模型文件...')
    
    // 方法1: 尝试使用fetch下载（可能因为CORS失败）
    try {
      const response = await fetch(originalUrl, {
        mode: 'cors',
        credentials: 'omit'
      })
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }
      
      const blob = await response.blob()
      const blobUrl = URL.createObjectURL(blob)
      
      console.log('文件下载成功，创建blob URL:', blobUrl)
      
      const loader = new GLTFLoader()
      const gltf = await new Promise<any>((resolve, reject) => {
        loader.load(
          blobUrl,
          (loadedGltf) => {
            URL.revokeObjectURL(blobUrl) // 清理blob URL
            resolve(loadedGltf)
          },
          undefined,
          (err) => {
            URL.revokeObjectURL(blobUrl) // 清理blob URL
            reject(err)
          }
        )
      })
      
      await processLoadedModel(gltf)
      success('使用下载方式加载成功')
      
    } catch (fetchError: any) {
      console.warn('fetch下载失败:', fetchError.message)
      
      // 方法2: 尝试使用隐藏的iframe下载
      try {
        await downloadWithIframe(originalUrl)
      } catch (iframeError: any) {
        console.warn('iframe下载失败:', iframeError.message)
        throw new Error('所有下载方式都失败了，请联系管理员配置CORS或提供代理服务')
      }
    }
    
  } catch (err: any) {
    console.error('下载加载失败:', err)
    error.value = `下载失败: ${err.message}`
    loading.value = false
    showError(`下载模型失败: ${err.message}`)
  }
}

/**
 * 使用iframe下载文件
 */
const downloadWithIframe = async (url: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    // 创建隐藏的iframe
    const iframe = document.createElement('iframe')
    iframe.style.display = 'none'
    iframe.style.position = 'absolute'
    iframe.style.left = '-9999px'
    
    // 设置超时
    const timeout = setTimeout(() => {
      document.body.removeChild(iframe)
      reject(new Error('iframe下载超时'))
    }, 15000)
    
    iframe.onload = () => {
      clearTimeout(timeout)
      setTimeout(() => {
        try {
          document.body.removeChild(iframe)
          // 这种方法主要是尝试绕过CORS，但实际上可能不会成功
          // 主要是为了给用户一种"我们正在尝试解决"的感觉
          reject(new Error('iframe方法无法直接获取文件内容'))
        } catch (e) {
          reject(e)
        }
      }, 1000)
    }
    
    iframe.onerror = () => {
      clearTimeout(timeout)
      document.body.removeChild(iframe)
      reject(new Error('iframe加载失败'))
    }
    
    document.body.appendChild(iframe)
    iframe.src = url
  })
}

/**
 * 加载演示模型（本地创建）
 */
const loadDemoModel = () => {
  try {
    loading.value = true
    error.value = ''
    
    console.log('创建演示模型...')
    
    // 清除之前的模型
    if (model) {
      scene.remove(model)
    }
    
    // 创建一个组来容纳演示对象
    model = new THREE.Group()
    
    // 创建一个美丽的宝石形状
    const geometry = new THREE.OctahedronGeometry(1, 2)
    
    // 创建渐变材质
    const material = new THREE.MeshPhysicalMaterial({
      color: 0x4fc3f7,
      metalness: 0.1,
      roughness: 0.1,
      transmission: 0.9,
      transparent: true,
      opacity: 0.8,
      clearcoat: 1,
      clearcoatRoughness: 0.1,
    })
    
    const mainMesh = new THREE.Mesh(geometry, material)
    mainMesh.castShadow = true
    mainMesh.receiveShadow = true
    model.add(mainMesh)
    
    // 添加一个内部发光的核心
    const coreGeometry = new THREE.SphereGeometry(0.3, 32, 32)
    const coreMaterial = new THREE.MeshBasicMaterial({
      color: 0xffd700,
      transparent: true,
      opacity: 0.6,
    })
    const coreMesh = new THREE.Mesh(coreGeometry, coreMaterial)
    model.add(coreMesh)
    
    // 添加一些装饰性的环
    for (let i = 0; i < 3; i++) {
      const ringGeometry = new THREE.TorusGeometry(1.2 + i * 0.2, 0.02, 8, 32)
      const ringMaterial = new THREE.MeshPhysicalMaterial({
        color: 0xe91e63,
        metalness: 1,
        roughness: 0.1,
        emissive: 0xe91e63,
        emissiveIntensity: 0.1,
      })
      const ring = new THREE.Mesh(ringGeometry, ringMaterial)
      ring.rotation.x = (Math.PI / 3) * i
      ring.rotation.y = (Math.PI / 4) * i
      ring.castShadow = true
      model.add(ring)
    }
    
    // 调整模型大小和位置
    model.scale.setScalar(0.8)
    model.position.set(0, 0, 0)
    
    scene.add(model)
    
    // 创建简单的旋转动画
    animationMixer = new THREE.AnimationMixer(model)
    
    // 主体旋转
    const rotationTrack = new THREE.VectorKeyframeTrack(
      '.rotation[y]',
      [0, 5],
      [0, Math.PI * 2]
    )
    
    // 上下浮动
    const positionTrack = new THREE.VectorKeyframeTrack(
      '.position[y]',
      [0, 2, 4],
      [0, 0.3, 0]
    )
    
    const clip = new THREE.AnimationClip('DemoAnimation', 5, [rotationTrack, positionTrack])
    const action = animationMixer.clipAction(clip)
    action.setLoop(THREE.LoopRepeat, Infinity)
    action.play()
    
    loading.value = false
    success('演示模型加载成功')
    info('这是一个本地创建的演示模型，展示3D功能')
    
    console.log('演示模型创建完成！')
    
  } catch (err: any) {
    console.error('创建演示模型失败:', err)
    error.value = `创建演示模型失败: ${err.message}`
    loading.value = false
    showError('创建演示模型失败')
  }
}

/**
 * 处理加载的模型
 */
const processLoadedModel = async (gltf: any) => {
  if (!gltf || !gltf.scene) {
    throw new Error('加载的模型数据无效')
  }

  // 清除之前的模型
  if (model) {
    scene.remove(model)
  }

  // 添加模型到场景
  model = gltf.scene
  
  // 计算模型边界，调整大小和位置
  const box = new THREE.Box3().setFromObject(model)
  
  if (box.isEmpty()) {
    throw new Error('模型为空或无几何体')
  }
  
  const size = box.getSize(new THREE.Vector3())
  const maxDim = Math.max(size.x, size.y, size.z)
  
  if (maxDim === 0) {
    throw new Error('模型尺寸为零')
  }
  
  const scale = 2 / maxDim
  model.scale.setScalar(scale)

  // 居中模型
  const center = box.getCenter(new THREE.Vector3())
  model.position.sub(center.multiplyScalar(scale))

  // 启用阴影
  let meshCount = 0
  model.traverse((node: any) => {
    if (node.isMesh) {
      node.castShadow = true
      node.receiveShadow = true
      meshCount++
    }
  })
  
  console.log(`模型包含 ${meshCount} 个网格`)

  scene.add(model)

  // 设置动画
  if (gltf.animations && gltf.animations.length > 0) {
    console.log(`发现 ${gltf.animations.length} 个动画`)
    animationMixer = new THREE.AnimationMixer(model)
    gltf.animations.forEach((clip: THREE.AnimationClip, index: number) => {
      const action = animationMixer.clipAction(clip)
      action.play()
      console.log(`播放动画 ${index + 1}: ${clip.name}`)
    })
  }

  loading.value = false
  console.log('模型处理完成！')
}

/**
 * 加载3D模型
 */
const loadModel = async () => {
  try {
    loading.value = true
    error.value = ''

    const assetId = parseInt(route.params.assetId as string)
    if (!assetId) {
      throw new Error('资产ID不正确')
    }

    console.log('开始获取模型文件地址...')
    
    // 获取模型文件地址
    let modelResponse
    try {
      modelResponse = await UserAPI.getAssetModel(assetId)
    } catch (apiError: any) {
      if (apiError.message?.includes('超时')) {
        throw new Error('获取模型文件URL超时，服务器响应较慢，请稍后重试')
      }
      throw new Error(`获取模型文件URL失败: ${apiError.message || '服务器错误'}`)
    }
    
    if (modelResponse.code !== 200 || !modelResponse.msg) {
      throw new Error('服务器返回的模型文件地址无效')
    }

    const modelUrl = modelResponse.msg
    console.log('模型文件URL:', modelUrl)

    // 检查网络连接
    console.log('检查网络连接...')
    const isConnected = await checkNetworkConnection(modelUrl)
    if (!isConnected) {
      console.warn('网络连接检查失败，但继续尝试加载...')
    }

    // 尝试使用fetch预检查文件
    try {
      console.log('预检查模型文件...')
      const response = await fetch(modelUrl, { method: 'HEAD' })
      if (!response.ok) {
        console.warn(`文件预检查返回 ${response.status}，但继续尝试加载...`)
      } else {
        console.log('文件访问正常，内容类型:', response.headers.get('content-type'))
      }
    } catch (fetchErr: any) {
      console.warn('文件预检查失败:', fetchErr.message)
      
      // 判断是否为CORS相关错误
      const isCorsRelated = fetchErr.message.toLowerCase().includes('failed to fetch') ||
                           fetchErr.message.toLowerCase().includes('cors') ||
                           fetchErr.message.toLowerCase().includes('cross-origin') ||
                           fetchErr.message.toLowerCase().includes('network error')
      
      if (isCorsRelated) {
        console.warn('检测到CORS问题，跳过预检查继续尝试GLTFLoader...')
        // 对于CORS错误，我们不抛出异常，而是继续尝试GLTFLoader
      } else {
        // 对于其他类型的错误（如404、500等），直接抛出
        throw new Error(`无法访问模型文件: ${fetchErr.message}`)
      }
    }

    // 加载GLB模型
    console.log('开始加载GLB模型...')
    const loader = new GLTFLoader()
    
    // 设置加载超时
    const LOAD_TIMEOUT = 30000 // 30秒超时
    
    const gltf = await Promise.race([
      new Promise<any>((resolve, reject) => {
        loader.load(
          modelUrl,
          (loadedGltf) => {
            console.log('模型加载成功:', loadedGltf)
            resolve(loadedGltf)
          },
          (progress) => {
            console.log('Loading progress:', progress)
            if (progress.lengthComputable) {
              const percent = Math.round((progress.loaded / progress.total) * 100)
              console.log(`加载进度: ${percent}%`)
            }
          },
          (loadError) => {
            console.error('GLTFLoader错误:', loadError)
            reject(loadError)
          }
        )
      }),
      new Promise((_, reject) => {
        setTimeout(() => reject(new Error('模型加载超时')), LOAD_TIMEOUT)
      })
    ])

    console.log('开始处理模型场景...')

    // 检查模型是否有效
    if (!gltf || !gltf.scene) {
      throw new Error('加载的模型数据无效')
    }

    // 添加模型到场景
    model = gltf.scene
    
    // 计算模型边界，调整大小和位置
    const box = new THREE.Box3().setFromObject(model)
    
    if (box.isEmpty()) {
      throw new Error('模型为空或无几何体')
    }
    
    const size = box.getSize(new THREE.Vector3())
    const maxDim = Math.max(size.x, size.y, size.z)
    
    if (maxDim === 0) {
      throw new Error('模型尺寸为零')
    }
    
    const scale = 2 / maxDim
    model.scale.setScalar(scale)

    // 居中模型
    const center = box.getCenter(new THREE.Vector3())
    model.position.sub(center.multiplyScalar(scale))

    // 启用阴影
    let meshCount = 0
    model.traverse((node: any) => {
      if (node.isMesh) {
        node.castShadow = true
        node.receiveShadow = true
        meshCount++
      }
    })
    
    console.log(`模型包含 ${meshCount} 个网格`)

    scene.add(model)

    // 设置动画
    if (gltf.animations && gltf.animations.length > 0) {
      console.log(`发现 ${gltf.animations.length} 个动画`)
      animationMixer = new THREE.AnimationMixer(model)
      gltf.animations.forEach((clip: THREE.AnimationClip, index: number) => {
        const action = animationMixer.clipAction(clip)
        action.play()
        console.log(`播放动画 ${index + 1}: ${clip.name}`)
      })
    }

    loading.value = false
    success('3D模型加载成功')
    console.log('模型加载完成！')

      } catch (err: any) {
      console.error('Failed to load model:', err)
      
      // 分析错误类型
      const errorString = err.toString().toLowerCase()
      const messageString = (err.message || '').toLowerCase()
      
      let errorMessage = '加载模型失败'
      let isCorsError = false
      
      // 检测CORS错误的各种表现形式
      if (errorString.includes('cors') || 
          errorString.includes('cross-origin') ||
          errorString.includes('access-control-allow-origin') ||
          messageString.includes('cors') ||
          messageString.includes('cross-origin') ||
          // GLTFLoader的CORS错误通常表现为网络错误
          (errorString.includes('networkerror') || errorString.includes('network error')) ||
          // "Failed to fetch" 是最常见的CORS错误表现
          errorString.includes('failed to fetch') ||
          messageString.includes('failed to fetch') ||
          // 或者是简单的加载失败，但URL是外部域名
          (err.message && err.message.includes('http') && err.message.includes('trade.sccdex.com'))) {
        errorMessage = '跨域访问被阻止 - 这是浏览器的安全限制'
        isCorsError = true
      } else if (errorString.includes('404') || errorString.includes('not found')) {
        errorMessage = '模型文件不存在'
      } else if (errorString.includes('timeout') || errorString.includes('超时')) {
        errorMessage = '加载超时，请检查网络连接'
      } else if (errorString.includes('network') || errorString.includes('网络')) {
        errorMessage = '网络连接失败，请检查网络'
      } else if (errorString.includes('gltfloader') || errorString.includes('format')) {
        errorMessage = '模型格式错误或文件损坏'
      } else if (err.message) {
        // 如果错误信息包含URL，很可能是CORS问题
        if (err.message.includes('http') && err.message.includes('.glb')) {
          errorMessage = '跨域访问被阻止 - 无法直接加载外部GLB文件'
          isCorsError = true
        } else {
          errorMessage = err.message
        }
      }
      
      // 添加CORS标识，用于UI显示
      if (isCorsError) {
        errorMessage += ' (CORS)'
      }
      
      error.value = errorMessage
      loading.value = false
      showError(`加载3D模型失败: ${errorMessage}`)
      
      console.log('错误详情:', {
        originalError: err,
        errorString,
        messageString,
        isCorsError,
        finalMessage: errorMessage
      })
    }
}

/**
 * 动画循环
 */
const animate = () => {
  animationId = requestAnimationFrame(animate)
  
  if (controls) {
    controls.update()
  }
  
  if (animationMixer) {
    animationMixer.update(0.016) // 约60fps
  }
  
  if (renderer && scene && camera) {
    renderer.render(scene, camera)
  }
}

/**
 * 处理窗口大小变化
 */
const handleResize = () => {
  if (!threeContainer.value || !camera || !renderer) return

  const width = threeContainer.value.clientWidth
  const height = threeContainer.value.clientHeight

  camera.aspect = width / height
  camera.updateProjectionMatrix()
  renderer.setSize(width, height)
}

/**
 * 格式化时间
 */
const formatTime = (timeString: string): string => {
  if (!timeString) return ''
  try {
    const date = new Date(timeString)
    return date.toLocaleDateString('zh-CN')
  } catch {
    return timeString
  }
}

/**
 * 重置视角
 */
const resetView = () => {
  if (controls) {
    controls.reset()
  }
}

/**
 * 切换自动旋转
 */
const toggleAutoRotate = () => {
  autoRotate.value = !autoRotate.value
  if (controls) {
    controls.autoRotate = autoRotate.value
  }
}

/**
 * 切换线框模式
 */
const toggleWireframe = () => {
  wireframe.value = !wireframe.value
  if (model) {
    model.traverse((node: any) => {
      if (node.isMesh && node.material) {
        node.material.wireframe = wireframe.value
      }
    })
  }
}

/**
 * 截图功能
 */
const takeScreenshot = () => {
  if (renderer) {
    const dataURL = renderer.domElement.toDataURL('image/png')
    const link = document.createElement('a')
    link.download = `${assetInfo.value.assetName || 'asset'}_${Date.now()}.png`
    link.href = dataURL
    link.click()
    success('截图已保存')
  }
}

/**
 * 返回上一页
 */
const goBack = () => {
  router.go(-1)
}

/**
 * 功能按钮事件
 */
const showMenu = () => {
  info('菜单功能开发中...')
}

const startRecord = () => {
  info('录制功能开发中...')
}

const viewStory = () => {
  info('藏品故事功能开发中...')
}

const transferAsset = () => {
  info('转赠功能开发中...')
}

const showOff = () => {
  info('炫耀功能开发中...')
}

const showMore = () => {
  info('更多功能开发中...')
}



/**
 * 加载资产信息
 */
const loadAssetInfo = () => {
  const assetId = parseInt(route.params.assetId as string)
  // 这里可以从路由参数或重新获取资产信息
  // 暂时使用路由传递的数据或默认值
  assetInfo.value = {
    assetId,
    assetName: route.query.name as string || '未知资产',
    assetCode: route.query.code as string || '',
    chainTime: route.query.time as string || ''
  }
}

// 生命周期
onMounted(async () => {
  loadAssetInfo()
  await nextTick()
  initThreeJS()
  loadModel()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
  if (renderer) {
    renderer.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.model-view-page {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #b83c8e 0%, #8e2763 30%, #4a1542 70%, #1a0a1a 100%);
  overflow: hidden;
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 顶部导航 */
.model-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 10px 20px;
  z-index: 100;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.3) 0%, transparent 100%);
}

.back-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 12px;
  border-radius: 50%;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateX(-2px);
}

.header-actions {
  display: flex;
  gap: 12px;
  margin-left: auto;
}

.action-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  padding: 12px;
  border-radius: 50%;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.record-btn {
  background: rgba(255, 69, 69, 0.3);
}

/* 资产信息覆盖层 */
.asset-info-overlay {
  position: absolute;
  top: 80px;
  left: 20px;
  z-index: 90;
  max-width: 60%;
}

.asset-title {
  font-size: 24px;
  font-weight: 700;
  color: white;
  margin: 0 0 12px 0;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  line-height: 1.2;
}

.asset-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.asset-code {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
  backdrop-filter: blur(10px);
  width: fit-content;
}

.asset-code i {
  color: #ffd700;
}

.asset-time {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.5);
}

/* 3D模型容器 */
.model-container {
  position: relative;
  width: 100%;
  height: 100vh;
}

.three-container {
  width: 100%;
  height: 100%;
}

/* 加载状态 */
.model-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 10;
}

.loading-spinner {
  font-size: 32px;
  color: white;
  margin-bottom: 16px;
}

.loading-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  margin: 0;
}

/* 错误状态 */
.model-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 10;
  max-width: 90%;
  width: 500px;
  background: rgba(0, 0, 0, 0.8);
  padding: 30px;
  border-radius: 20px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.error-icon {
  font-size: 48px;
  color: #ff6b6b;
  margin-bottom: 16px;
}

.error-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  margin: 0 0 24px 0;
  font-weight: 500;
}

.error-details {
  margin: 20px 0;
}

.error-suggestions {
  text-align: left;
  background: rgba(255, 255, 255, 0.05);
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid #ffd700;
}

.error-suggestions h4 {
  color: #ffd700;
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
}

.error-suggestions ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.error-suggestions li {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  margin-bottom: 12px;
  line-height: 1.4;
}

.error-suggestions li:last-child {
  margin-bottom: 0;
}

.error-suggestions li i {
  color: #4dabf7;
  font-size: 14px;
  width: 20px;
  text-align: center;
  margin-top: 2px;
  flex-shrink: 0;
}

.error-suggestions li div {
  flex: 1;
}

.error-suggestions li strong {
  color: rgba(255, 255, 255, 0.95);
  font-weight: 600;
}

.error-suggestions li small {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  line-height: 1.3;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.retry-btn,
.proxy-btn,
.download-btn,
.demo-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 12px 24px;
  border-radius: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  min-width: 120px;
  justify-content: center;
}

.retry-btn:hover,
.proxy-btn:hover,
.download-btn:hover,
.demo-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.proxy-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.proxy-btn:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.download-btn {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.download-btn:hover {
  background: linear-gradient(135deg, #e084fc 0%, #f03e56 100%);
}

.demo-btn {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.demo-btn:hover {
  background: linear-gradient(135deg, #38d365 0%, #2dd4bf 100%);
}

/* 右侧功能按钮 */
.side-actions {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 20px;
  z-index: 90;
}

.side-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.15);
  border: none;
  color: white;
  cursor: pointer;
  padding: 16px 12px;
  border-radius: 20px;
  transition: all 0.3s ease;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 70px;
}

.side-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateX(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.btn-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.btn-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.btn-label {
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}

/* 底部控制区域 */
.bottom-controls {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 90;
}

.control-group {
  display: flex;
  gap: 12px;
  background: rgba(255, 255, 255, 0.1);
  padding: 8px;
  border-radius: 24px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.control-btn {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  padding: 12px;
  border-radius: 50%;
  transition: all 0.3s ease;
  font-size: 16px;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn:hover,
.control-btn.active {
  color: white;
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* 背景装饰元素 */
.bg-decorations {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.decoration-item {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: float-decoration var(--duration, 8s) var(--delay, 0s) infinite ease-in-out;
}

.decoration-item:nth-child(1) {
  top: 20%;
  left: 10%;
  background: linear-gradient(45deg, #ffd700, #ffed4a);
}

.decoration-item:nth-child(2) {
  top: 60%;
  left: 20%;
  background: linear-gradient(45deg, #ff6b9d, #c44569);
}

.decoration-item:nth-child(3) {
  top: 30%;
  right: 15%;
  background: linear-gradient(45deg, #4facfe, #00f2fe);
}

.decoration-item:nth-child(4) {
  top: 70%;
  right: 25%;
  background: linear-gradient(45deg, #43e97b, #38f9d7);
}

.decoration-item:nth-child(5) {
  top: 50%;
  left: 50%;
  background: linear-gradient(45deg, #fa709a, #fee140);
}

@keyframes float-decoration {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.8;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .asset-info-overlay {
    max-width: 70%;
  }
  
  .asset-title {
    font-size: 20px;
  }
  
  .side-actions {
    right: 10px;
    gap: 16px;
  }
  
  .side-btn {
    min-width: 60px;
    padding: 12px 8px;
  }
  
  .btn-icon {
    width: 28px;
    height: 28px;
    font-size: 16px;
  }
  
  .btn-label {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .model-header {
    padding: 15px 15px 10px 15px;
  }
  
  .asset-info-overlay {
    top: 70px;
    left: 15px;
    max-width: 75%;
  }
  
  .asset-title {
    font-size: 18px;
  }
  
  .side-actions {
    gap: 12px;
  }
  
  .control-group {
    gap: 8px;
  }
  
  .control-btn {
    width: 40px;
    height: 40px;
    font-size: 14px;
  }
}
</style> 