<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getAssetDetail } from '@/api/exhibition'
import type { AssetItem, IssuerInfo } from '@/api/exhibition'
import type { ApiResponse } from '@/api/types'
import { DictionaryAPI } from '@/api/dictionary'
import type { DictionaryItem } from '@/api/dictionary'
import { PayAPI } from '@/api'
import type { AddOrderRequest, AddOrderResponse } from '@/api/pay'
import { useNotification } from '@/composables/useNotification'

// 路由
const route = useRoute()
const router = useRouter()

// 通知系统
const notification = useNotification()

// 数据状态
const asset = ref<AssetItem | null>(null)
const loading = ref(false)
const error = ref('')

// 图片展示相关
const showImageModal = ref(false)

// 倒计时相关
const currentTime = ref(new Date())
const countdownTimer = ref<number | null>(null)

// 资产类型字典数据
const assetTypesList = ref<DictionaryItem[]>([])
const isLoadingAssetTypes = ref(false)

// 购买相关状态
const isPurchasing = ref(false)
const showPurchaseModal = ref(false)
const purchaseQuantity = ref(1)

// 计算属性
const assetId = computed(() => route.params.assetId as string)

// 判断是否已到发售时间
const isSaleStarted = computed(() => {
  if (!asset.value?.saleStartTime) return true // 如果没有发售时间，默认可以购买
  const saleTime = new Date(asset.value.saleStartTime)
  return currentTime.value >= saleTime
})

// 计算倒计时
const countdown = computed(() => {
  if (!asset.value?.saleStartTime || isSaleStarted.value) return null
  
  const saleTime = new Date(asset.value.saleStartTime)
  const diff = saleTime.getTime() - currentTime.value.getTime()
  
  if (diff <= 0) return null
  
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((diff % (1000 * 60)) / 1000)
  
  return { days, hours, minutes, seconds }
})

const getIntroImages = (): string[] => {
  if (asset.value?.introImages) {
    return asset.value.introImages.split(',').map(img => img.trim()).filter(img => img)
  }
  return []
}

const formatPrice = (price: any): string => {
  // 如果未到开售时间，显示???
  if (!isSaleStarted.value) {
    return '¥-.-'
  }
  
  if (typeof price === 'number') {
    return price.toFixed(2)
  }
  return String(price || '0.00').replace(/[¥]/g, '')
}

const formatDateTime = (dateStr: string): string => {
  if (!dateStr) return '待定'
  try {
    const date = new Date(dateStr)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    
    return `${year}-${month}-${day}`
  } catch (error) {
    return '待定'
  }
}

const getKeywords = (): string[] => {
  if (asset.value?.assetKeywords) {
    return asset.value.assetKeywords.split(',').map(k => k.trim()).filter(k => k)
  }
  return []
}

const getIssuerInfo = (): IssuerInfo[] => {
  if (asset.value?.issuers && Array.isArray(asset.value.issuers) && asset.value.issuers.length > 0) {
    return asset.value.issuers
  }
  return []
}

const getStatusInfo = () => {
  if (!asset.value?.statusCd) return { text: '未知状态', type: 'default' }
  
  switch (asset.value.statusCd) {
    case 'ON_SALE':
      return { text: '在售中', type: 'on-sale' }
    case 'SOLD_OUT':
      return { text: '已售罄', type: 'sold-out' }
    case 'LIMITED':
      return { text: '限量发售', type: 'limited' }
    case 'PRESALE':
      return { text: '预售中', type: 'presale' }
    default:
      return { text: asset.value.statusCd, type: 'default' }
  }
}

// 获取资产类型字典数据
const loadAssetTypes = async () => {
  try {
    isLoadingAssetTypes.value = true
    console.log('📡 开始请求资产类型字典数据...')
    const response = await DictionaryAPI.getDictionary('dig_asset_type')
    
    console.log('📥 资产类型字典API响应:', response)
    
    if (response.code === 200 && response.data && response.data.length > 0) {
      assetTypesList.value = response.data
      console.log('✅ 资产类型字典数据加载成功:', assetTypesList.value.length, '条')
    } else {
      assetTypesList.value = []
      console.warn('⚠️ 资产类型字典接口返回空数据')
    }
  } catch (error) {
    console.error('❌ 获取资产类型字典失败:', error)
    assetTypesList.value = []
  } finally {
    isLoadingAssetTypes.value = false
  }
}

// 根据资产类型值获取对应的汉字标签
const getAssetTypeLabel = (assetTypeValue: string): string => {
  if (!assetTypeValue || !assetTypesList.value.length) {
    return assetTypeValue || ''
  }
  
  const found = assetTypesList.value.find(item => item.value === assetTypeValue)
  return found ? found.label : assetTypeValue
}

// 方法
const fetchAssetDetail = async () => {
  try {
    loading.value = true
    error.value = ''
    
    const response = await getAssetDetail(assetId.value) as ApiResponse<AssetItem>
    
    if (response && response.code === 200 && response.data) {
      asset.value = response.data
    } else {
      error.value = '获取资产详情失败'
    }
  } catch (err) {
    console.error('获取资产详情失败:', err)
    error.value = '网络错误，请稍后重试'
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.back()
}

// 跳转到系列页面
const goToSeries = () => {
  if (asset.value?.seriesId) {
    router.push(`/series/${asset.value.seriesId}`)
  }
}

const goToZone = () => {
  if (asset.value?.zoneId) {
    router.push(`/zone/${asset.value.zoneId}`)
  }
}

const showCoverImage = () => {
  showImageModal.value = true
}

const closeImageModal = () => {
  showImageModal.value = false
}

// 启动倒计时定时器
const startCountdown = () => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
  }
  
  countdownTimer.value = setInterval(() => {
    currentTime.value = new Date()
  }, 1000) as unknown as number
}

// 清除倒计时定时器
const clearCountdown = () => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
}

// 购买相关方法

/**
 * 立即购买
 */
const handlePurchase = async () => {
  if (!asset.value?.assetId) {
    notification.error('资产信息加载中，请稍后再试')
    return
  }

  // 检查是否已到发售时间
  if (!isSaleStarted.value) {
    notification.warning('商品尚未开始发售，请等待发售时间')
    return
  }

  try {
    isPurchasing.value = true
    
    // 构建下单参数
    const orderData: AddOrderRequest = {
      buyQuantity: purchaseQuantity.value,
      tradeType: 'issueBuy',
      // payChannel: 'alipayH5',
      assetId: String(asset.value.assetId)
    }

    console.log('🛒 开始下单:', orderData)
    
    // 调用下单接口
    const response = await PayAPI.addOrder(orderData)
    
    if (response.code === 200 && response.data) {
      console.log('✅ 下单成功:', response.data)
      
      // 检查返回的数据类型，进行相应处理
      let paymentData = response.data
      
      // 情况1：如果返回的data直接是URL字符串（新的支付方式）
      if (typeof response.data === 'string' && 
          (response.data as string).startsWith('http') && 
          !(response.data as string).includes('<form')) {
        const paymentUrl = response.data as string
        console.log('🔗 检测到API直接返回支付URL:', paymentUrl)
        // 包装成对象格式，以便统一处理
        paymentData = {
          payUrl: paymentUrl,
          orderId: '', // 可以尝试从URL参数中提取订单号
        } as AddOrderResponse
        
        // 尝试从URL参数中提取订单号
        try {
          const url = new URL(paymentUrl)
          const orderIdFromUrl = url.searchParams.get('out_trade_no') || 
                                url.searchParams.get('orderId') || 
                                url.searchParams.get('order_id')
          if (orderIdFromUrl) {
            paymentData.orderId = orderIdFromUrl
            console.log('📋 从支付URL中提取到订单号:', paymentData.orderId)
          }
        } catch (urlError) {
          console.warn('⚠️ 解析支付URL参数失败:', urlError)
        }
      }
      // 情况2：如果返回的data直接是HTML字符串（支付宝H5支付表单）
      else if (typeof response.data === 'string' && (response.data as string).includes('<form')) {
        const htmlString = response.data as string
        console.log('🔍 检测到API直接返回HTML表单字符串')
        // 包装成对象格式，以便统一处理
        paymentData = {
          payForm: htmlString,
          orderId: '', // 尝试从HTML中提取订单号
        } as AddOrderResponse
        
        // 尝试从HTML中提取订单号
        const orderIdMatch = htmlString.match(/out_trade_no["']\s*:\s*["'](.*?)["']/i)
        if (orderIdMatch && orderIdMatch[1]) {
          paymentData.orderId = orderIdMatch[1]
          console.log('📋 从HTML表单中提取到订单号:', paymentData.orderId)
        }
      }
      // 情况3：返回的是对象格式（标准格式）
      else if (typeof response.data === 'object') {
        console.log('📦 检测到API返回标准对象格式')
        paymentData = response.data
      }
      
      // 处理支付返回的数据
      await handlePaymentResponse(paymentData)
    } else {
      throw new Error(response.msg || '下单失败')
    }
  } catch (error) {
    console.error('❌ 下单失败:', error)
    
    const errorMessage = error instanceof Error ? error.message : '下单失败，请重试'
    notification.error(errorMessage)
  } finally {
    isPurchasing.value = false
  }
}

/**
 * 处理支付响应数据 - 跳转到支付页面
 */
const handlePaymentResponse = async (paymentData: AddOrderResponse) => {
  try {
    console.log('💳 处理支付响应数据:', paymentData)
    
    // 提取订单号
    let orderNo = ''
    
    // 情况1：从paymentData对象中获取订单号
    if (paymentData.orderId) {
      orderNo = paymentData.orderId
    } 
    // 情况2：从paymentData对象中获取orderNo
    else if ((paymentData as any).orderNo) {
      orderNo = (paymentData as any).orderNo
    }
    // 情况3：如果返回的是URL字符串，尝试从URL中提取订单号
    else if (typeof paymentData === 'string' && (paymentData as string).startsWith('http')) {
      try {
        const url = new URL(paymentData as string)
        orderNo = url.searchParams.get('out_trade_no') || 
                  url.searchParams.get('orderId') || 
                  url.searchParams.get('order_id') || 
                  url.searchParams.get('orderNo') || ''
      } catch (urlError) {
        console.warn('⚠️ 从URL提取订单号失败:', urlError)
      }
    }
    // 情况4：如果返回的是HTML表单，尝试从HTML中提取订单号
    else if (typeof paymentData === 'string' && (paymentData as string).includes('<form')) {
      const htmlString = paymentData as string
      const orderIdMatch = htmlString.match(/out_trade_no["']\s*[:=]\s*["'](.*?)["']/i) ||
                          htmlString.match(/orderId["']\s*[:=]\s*["'](.*?)["']/i) ||
                          htmlString.match(/order_id["']\s*[:=]\s*["'](.*?)["']/i) ||
                          htmlString.match(/orderNo["']\s*[:=]\s*["'](.*?)["']/i)
      if (orderIdMatch && orderIdMatch[1]) {
        orderNo = orderIdMatch[1]
      }
    }
    
    if (orderNo) {
      console.log('📋 提取到订单号:', orderNo)
      
      // 保存订单号到本地存储
      localStorage.setItem('pendingOrderId', orderNo)
      
      // 显示成功提示
      notification.success('订单创建成功，正在跳转到支付页面...', 1500)
      
      // 跳转到支付页面，传递必要的参数
      setTimeout(() => {
        router.push({
          name: 'payment',
          params: {
            orderNo: orderNo
          },
          query: {
            assetName: asset.value?.assetName || '',
            assetPrice: asset.value?.issuePrice?.toString() || '0',
            assetCover: asset.value?.assetCover || '',
            from: 'asset-detail' // 标记来源页面
          }
        })
      }, 800)
      
    } else {
      console.warn('⚠️ 未能提取到订单号，使用备用处理方案')
      
      // 如果无法提取订单号，但创建订单成功，跳转到我的订单页面
      notification.success('订单创建成功！请前往我的订单页面查看')
      
      setTimeout(() => {
        router.push('/my-orders')
      }, 1000)
    }
    
  } catch (error) {
    console.error('❌ 处理支付响应失败:', error)
    notification.error('订单创建成功，但跳转支付页面失败，请前往我的订单页面查看')
    
    // 发生错误时，延迟跳转到我的订单页面
    setTimeout(() => {
      router.push('/my-orders')
    }, 2000)
  }
}

/**
 * 显示购买确认模态框
 */
const showPurchaseConfirm = () => {
  if (!asset.value) return
  
  // 重置购买数量
  purchaseQuantity.value = 1
  showPurchaseModal.value = true
}

/**
 * 关闭购买模态框
 */
const closePurchaseModal = () => {
  showPurchaseModal.value = false
}

/**
 * 确认购买
 */
const confirmPurchase = () => {
  closePurchaseModal()
  handlePurchase()
}

// 生命周期
onMounted(async () => {
  // 并行加载资产详情和字典数据
  await Promise.all([
    fetchAssetDetail(),
    loadAssetTypes()
  ])
  startCountdown()
})

onUnmounted(() => {
  clearCountdown()
})
</script>

<template>
  <div class="asset-detail-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <i class="fas fa-spinner fa-spin"></i>
      <p>正在加载资产详情...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <i class="fas fa-exclamation-triangle"></i>
      <p>{{ error }}</p>
      <button class="retry-btn" @click="fetchAssetDetail">重试</button>
    </div>

    <!-- 详情内容 -->
    <div v-else-if="asset" class="detail-content">
      <!-- 封面图区域 - 图片为主的大幅展示 -->
      <section class="cover-section">
        <div class="cover-display" v-if="asset.assetCover">
          <!-- 主图片 -->
          <div class="main-image-container">
            <!-- 返回按钮 -->
            <button class="back-btn" @click="goBack">
              <i class="fas fa-arrow-left"></i>
            </button>
            
            <img 
              :src="asset.assetCover" 
              :alt="asset.assetName"
              @click="showCoverImage"
              class="main-image"
            >
            <!-- 悬浮操作层 -->
            <div class="image-overlay">
              <div class="overlay-actions">
                <button class="zoom-btn" @click="showCoverImage">
                  <i class="fas fa-search-plus"></i>
                </button>
              </div>
            </div>
          </div>
          
          <!-- 图片底部信息 -->
          <div class="image-caption">
            <div class="caption-content">
              <span class="asset-title">{{ asset.assetName }}</span>
              <!-- <span class="asset-type">数字藏品</span> -->
            </div>
          </div>
        </div>
        
        <!-- 占位图 -->
        <div v-else class="image-placeholder">
          <div class="placeholder-icon">
            <i class="fas fa-image"></i>
          </div>
          <p class="placeholder-text">暂无封面图</p>
        </div>
      </section>

      <!-- 基本信息 -->
      <section class="basic-info">
        <!-- 重新设计的资产信息区域 - 统一大卡片 -->
        <div v-if="getKeywords().length > 0 || asset?.seriesName || asset?.zoneName || asset?.assetType || getIssuerInfo().length > 0" class="asset-info-unified">
          
          <!-- 发行方信息行 -->
          <div v-if="getIssuerInfo().length > 0" class="info-row issuer-row">
            <div class="row-label">发行方</div>
            <div class="row-content">
              <div class="issuer-card" v-for="(issuer, index) in getIssuerInfo()" :key="`issuer-${index}-${issuer.issuerId || issuer.issuerName || issuer.name || index}`">
                <div class="issuer-logo">
                  <img 
                    v-if="issuer.issuerLogo || issuer.logo" 
                    :src="issuer.issuerLogo || issuer.logo" 
                    :alt="issuer.issuerName || issuer.name"
                    class="issuer-logo-img"
                  />
                  <div v-else class="issuer-logo-placeholder">
                    <span>{{ (issuer.issuerName || issuer.name || '发行方').charAt(0) }}</span>
                  </div>
                </div>
                <div class="issuer-info">
                  <div class="issuer-name">{{ issuer.issuerName || issuer.name }}</div>
                  <div class="issuer-subtitle">官方认证</div>
                </div>
                <div class="verified-badge">
                  <i class="fas fa-certificate"></i>
                </div>
              </div>
            </div>
          </div>

          <!-- 分类信息行 -->
          <div v-if="asset?.seriesName || asset?.zoneName || asset?.assetType" class="info-row category-row">
            <div class="row-label">分类信息</div>
            <div class="row-content">
              <div class="category-tags">
                <!-- 所属系列 -->
                <div 
                  v-if="asset?.seriesName" 
                  class="category-tag tag-series"
                  :class="{ 'clickable': asset?.seriesId }"
                  @click="goToSeries"
                >
                  <span class="tag-label">系列</span>
                  <span class="tag-value">{{ asset.seriesName }}</span>
                </div>
                
                <!-- 所属专区 -->
                <div 
                  v-if="asset?.zoneName" 
                  class="category-tag tag-zone"
                  :class="{ 'clickable': asset?.zoneId }"
                  @click="goToZone"
                >
                  <span class="tag-label">专区</span>
                  <span class="tag-value">{{ asset.zoneName }}</span>
                </div>
                
                <!-- 资产类型 -->
                <div v-if="asset?.assetType" class="category-tag tag-type">
                  <span class="tag-label">类型</span>
                  <span class="tag-value">{{ getAssetTypeLabel(asset.assetType) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 标签信息行 -->
          <div v-if="getKeywords().length > 0" class="info-row tags-row">
            <div class="row-label">标签</div>
            <div class="row-content">
              <div class="keyword-tags">
                <span 
                  v-for="keyword in getKeywords()" 
                  :key="`keyword-${keyword}`"
                  class="keyword-tag"
                >
                  {{ keyword }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 价格信息 -->
        <!-- <div class="price-section">
          <div class="price-info">
            <span class="price-label">发行价格</span>
            <span class="price-value">¥{{ formatPrice(asset.issuePrice) }}</span>
          </div>
          
          <div v-if="asset.issueQuantity" class="quantity-info">
            <span class="quantity-label">发行数量</span>
            <span class="quantity-value">{{ asset.issueQuantity }} 份</span>
          </div>
        </div> -->
      </section>

      <!-- 详细信息 -->
      <section class="detail-info">
        <!-- 基础属性 -->
        <!-- <div class="info-card">
          <h3 class="card-title">基础信息</h3>
          <div class="info-list">
            <div v-if="asset.assetType" class="info-item">
              <span class="info-label">资产类型</span>
              <span class="info-value">{{ asset.assetType }}</span>
            </div>
            <div v-if="asset.assetLevel" class="info-item">
              <span class="info-label">资产等级</span>
              <span class="info-value">{{ asset.assetLevel }}</span>
            </div>
            <div v-if="asset.saleStartTime" class="info-item">
              <span class="info-label">开售时间</span>
              <span class="info-value">{{ formatDateTime(asset.saleStartTime) }}</span>
            </div>
            <div v-if="asset.seriesId" class="info-item">
              <span class="info-label">所属系列</span>
              <span class="info-value">系列 {{ asset.seriesId }}</span>
            </div>
          </div>
        </div> -->

        <!-- 限购信息 -->
        <!-- <div v-if="asset.individualLimit || asset.enterpriseLimit" class="info-card">
          <h3 class="card-title">限购信息</h3>
          <div class="info-list">
            <div v-if="asset.individualLimit" class="info-item">
              <span class="info-label">个人限购</span>
              <span class="info-value">{{ asset.individualLimit }} 份</span>
            </div>
            <div v-if="asset.enterpriseLimit" class="info-item">
              <span class="info-label">企业限购</span>
              <span class="info-value">{{ asset.enterpriseLimit }} 份</span>
            </div>
          </div>
        </div> -->

        <!-- 发行信息 -->
        <!-- <div class="info-card">
          <h3 class="card-title">发行信息</h3>
          <div class="info-list">
            <div v-if="asset.airdropQuantity" class="info-item">
              <span class="info-label">空投数量</span>
              <span class="info-value">{{ asset.airdropQuantity }} 份</span>
            </div>
            <div v-if="asset.activityQuantity" class="info-item">
              <span class="info-label">活动数量</span>
              <span class="info-value">{{ asset.activityQuantity }} 份</span>
            </div>
            <div v-if="asset.createDate" class="info-item">
              <span class="info-label">创建时间</span>
              <span class="info-value">{{ formatDateTime(asset.createDate) }}</span>
            </div>
          </div>
        </div> -->
      </section>

      <!-- 资产描述 -->
      <section v-if="asset.assetDesc || getIntroImages().length > 0" class="description-section">
        <div class="section-header">
          <h3 class="section-title">资产介绍</h3>
          <div class="section-divider"></div>
        </div>
        
        <!-- 介绍图片 -->
        <div v-if="getIntroImages().length > 0" class="intro-images">
          <div 
            v-for="(image, index) in getIntroImages()" 
            :key="index"
            class="intro-image-container"
          >
            <img 
              :src="image" 
              :alt="`${asset.assetName} 介绍图 ${index + 1}`"
              class="intro-image"
            >
            <div class="image-overlay"></div>
          </div>
        </div>
      </section>

      <!-- 购买须知 -->
      <section class="purchase-notice-section">
        <div class="section-header">
          <h3 class="section-title">购买须知</h3>
          <div class="section-divider"></div>
        </div>
        
        <div class="notice-content">
          <div class="notice-text">
            数字资产仅限实名认证为年满18周岁的中国大陆用户购买。数字资产的版权由发行方或原创者拥有，除另外取得版权拥有者书面同意外，用户不得将数字资产用于任何商业用途。本平台的用户享有部分数字资产"七日无理由退货"规则内的相关权益，但在特殊情况下，部分数字资产不支持退货退款，具体以每款数字资产展示页面内的权益标签及相关说明内容为准，且本商品源文件不支持本地下载。请勿对数字资产进行炒作、场外交易、欺诈，或以任何其他非法方式进行使用。如果因在其他平台私自购买产生的购买纠纷，我司不承担任何责任。
          </div>
        </div>
      </section>

      <!-- 底部固定操作区 -->
      <section class="bottom-fixed-section">
        <!-- 操作按钮区 -->
        <div class="action-buttons">
          <!-- 发售价格显示 -->
          <div class="price-display">
            <div class="price-content">
              <!-- <div class="price-header">
                <i class="fas fa-tag price-icon"></i>
                <span class="price-label">发售价格</span>
              </div> -->
              <div class="price-info">
                <span v-if="isSaleStarted" class="price-value">¥</span><span class="price-value">{{ formatPrice(asset.issuePrice) }}</span>
              </div>
            </div>

          </div>
          
          <!-- 根据发售状态显示不同内容 -->
          <button 
            v-if="isSaleStarted" 
            class="action-btn primary"
            :disabled="isPurchasing"
            @click="handlePurchase"
          >
            <i v-if="isPurchasing" class="fas fa-spinner fa-spin"></i>
            <i v-else class="fas fa-shopping-cart"></i>
            {{ isPurchasing ? '处理中...' : '立即购买' }}
          </button>
          
          <!-- 倒计时显示 -->
          <div v-else-if="countdown" class="countdown-display">
            <div class="countdown-content">
              <!-- <div class="countdown-header">
                <i class="fas fa-clock countdown-icon"></i>
                <span class="countdown-text">距离开售</span>
              </div> -->
              <div class="countdown-time">
                <span v-if="countdown.days > 0" class="time-unit">
                  <span class="time-number">{{ countdown.days }}</span>
                  <span class="time-label">天</span>
                </span>
                <span class="time-unit">
                  <span class="time-number">{{ String(countdown.hours).padStart(2, '0') }}</span>
                  <span class="time-label">时</span>
                </span>
                <span class="time-unit">
                  <span class="time-number">{{ String(countdown.minutes).padStart(2, '0') }}</span>
                  <span class="time-label">分</span>
                </span>
                <span class="time-unit">
                  <span class="time-number">{{ String(countdown.seconds).padStart(2, '0') }}</span>
                  <span class="time-label">秒</span>
                </span>
              </div>
            </div>
            <div class="countdown-pulse"></div>
          </div>
          
          <!-- 默认状态（无发售时间或已过期） -->
          <button 
            v-else 
            class="action-btn primary"
            :disabled="isPurchasing"
            @click="handlePurchase"
          >
            <i v-if="isPurchasing" class="fas fa-spinner fa-spin"></i>
            <i v-else class="fas fa-shopping-cart"></i>
            {{ isPurchasing ? '处理中...' : '立即购买' }}
          </button>
        </div>
      </section>
    </div>

    <!-- 封面图预览模态框 -->
    <div v-if="showImageModal && asset?.assetCover" class="image-modal" @click="closeImageModal">
      <div class="modal-content" @click.stop>
        <button class="modal-close" @click="closeImageModal">
          <i class="fas fa-times"></i>
        </button>
        <img 
          :src="asset.assetCover" 
          :alt="asset.assetName"
          class="modal-image"
        >
      </div>
    </div>
  </div>
</template>

<style scoped>
/* CSS变量定义 - 深色金黄主题配色方案 */
:root {
  /* 主色系 - 优雅金黄 */
  --primary-color: #d4a574;
  --primary-dark: #b8956a;
  --primary-light: #e8c49a;
  --primary-alpha: rgba(212, 165, 116, 0.15);
  
  /* 辅助色系 - 温暖橙色 */
  --accent-color: #f4a261;
  --accent-dark: #e76f51;
  --accent-light: #f9c74f;
  --accent-alpha: rgba(244, 162, 97, 0.15);
  
  /* 功能色系 */
  --success-color: #90a955;
  --warning-color: #f9844a;
  --error-color: #e63946;
  --info-color: #457b9d;
  
  /* 文字颜色系统 */
  --text-primary: #f8f6f0;
  --text-secondary: #e0ddd4;
  --text-tertiary: #c4c0b1;
  --text-muted: #a39d8e;
  --text-inverse: #2d2a24;
  
  /* 背景色系统 */
  --bg-primary: #1a1a1a;
  --bg-secondary: #242424;
  --bg-tertiary: #2e2e2e;
  --bg-card: rgba(42, 42, 42, 0.9);
  --bg-card-hover: rgba(50, 50, 50, 0.95);
  --bg-glass: rgba(42, 42, 42, 0.8);
  
  /* 边框和阴影 */
  --border-primary: rgba(212, 165, 116, 0.3);
  --border-light: rgba(248, 246, 240, 0.1);
  --border-hover: rgba(212, 165, 116, 0.5);
  
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.6);
  --shadow-primary: 0 4px 16px rgba(212, 165, 116, 0.3);
  --shadow-glow: 0 0 20px rgba(212, 165, 116, 0.4);
  
  /* 渐变系统 */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
  --gradient-bg: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
  --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(46, 46, 46, 0.8) 100%);
  --gradient-hero: linear-gradient(180deg, rgba(26, 26, 26, 0.8) 0%, rgba(36, 36, 36, 0.9) 50%, rgba(46, 46, 46, 0.95) 100%);
  
  /* 兼容旧变量名 */
  --primary-gold: var(--primary-color);
  --primary-gold-light: var(--primary-light);
  --primary-gold-dark: var(--primary-dark);
  --primary-gold-alpha: var(--primary-alpha);
  --secondary-purple: var(--accent-color);
  --secondary-purple-light: var(--accent-light);
  --secondary-purple-dark: var(--accent-dark);
  --neutral-100: var(--text-primary);
  --neutral-200: var(--text-secondary);
  --neutral-400: var(--text-tertiary);
  --neutral-600: var(--text-muted);
  --shadow-gold: var(--shadow-primary);
  --shadow-purple: var(--shadow-primary);
}

.asset-detail-container {
  background: var(--gradient-hero);
  color: var(--neutral-100);
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  min-height: 100vh;
}

/* 封面图样式 - 图片为主的大幅展示 */
.cover-section {
  margin-bottom: 20px;
}

/* 图片展示容器 */
.cover-display {
  position: relative;
  background: var(--bg-primary);
  border-radius: 0 0 16px 16px;
  overflow: hidden;
}

/* 主图片容器 */
.main-image-container {
  position: relative;
  width: 100%;
  height: 515px;
  aspect-ratio: 1;
  overflow: hidden;
}

/* 返回按钮 */
.back-btn {
  position: absolute;
  top: 40px;
  left: 16px;
  z-index: 20;
  background: rgba(0, 0, 0, 0.6);
  border: none;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(8px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.back-btn:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

.back-btn i {
  font-size: 16px;
}

.main-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.3s ease;
  display: block;
}

.main-image:hover {
  transform: scale(1.02);
}

/* 悬浮操作层 */
.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.main-image-container:hover .image-overlay {
  opacity: 1;
}

.overlay-actions {
  display: flex;
  align-items: center;
  justify-content: center;
}

.zoom-btn {
  background: rgba(212, 165, 116, 0.9);
  border: none;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(4px);
}

.zoom-btn:hover {
  background: var(--primary-color);
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

.zoom-btn i {
  font-size: 20px;
}

/* 图片底部标题 - 重新设计的艺术标题 */
.image-caption {
  background: linear-gradient(
    to top, 
    rgba(0, 0, 0, 0.9) 0%, 
    rgba(0, 0, 0, 0.85) 15%, 
    rgba(0, 0, 0, 0.75) 30%, 
    rgba(0, 0, 0, 0.6) 45%, 
    rgba(0, 0, 0, 0.4) 60%, 
    rgba(0, 0, 0, 0.2) 75%, 
    rgba(0, 0, 0, 0.05) 90%, 
    transparent 100%
  );
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 32px 20px 20px 20px;
  color: white;
  backdrop-filter: blur(8px);
}

.caption-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: relative;
}

.caption-content::before {
  content: '';
  position: absolute;
  top: -16px;
  left: 0;
  width: 40px;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color) 0%, transparent 100%);
  border-radius: 1px;
}

.asset-title {
  font-size: 24px;
  font-weight: 800;
  color: #ffffff;
  text-shadow: 0 4px 12px rgba(0, 0, 0, 0.8);
  line-height: 1.1;
  letter-spacing: -0.5px;
  position: relative;
}

.asset-title::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, var(--primary-color) 0%, transparent 30%);
  opacity: 0.6;
}



.asset-type {
  font-size: 11px;
  color: var(--primary-color);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  opacity: 0.9;
  padding: 2px 8px;
  background: rgba(212, 165, 116, 0.15);
  border: 1px solid rgba(212, 165, 116, 0.3);
  border-radius: 8px;
  display: inline-block;
  backdrop-filter: blur(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  width: fit-content;
}

/* 占位图样式 */
.image-placeholder {
  aspect-ratio: 1;
  background: var(--bg-secondary);
  border-radius: 0 0 16px 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--neutral-400);
  border: 2px dashed rgba(212, 165, 116, 0.3);
  gap: 16px;
}

.placeholder-icon i {
  font-size: 64px;
  color: var(--primary-color);
  opacity: 0.5;
}

.placeholder-text {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--neutral-300);
}

/* 响应式优化 */
@media (max-width: 480px) {
  .cover-display {
    border-radius: 0 0 12px 12px;
  }
  
  .zoom-btn {
    width: 50px;
    height: 50px;
  }
  
  .zoom-btn i {
    font-size: 18px;
  }
  
     .image-caption {
     padding: 24px 16px 16px 16px;
   }
   
   .caption-content::before {
     width: 30px;
     top: -12px;
   }
   
   .asset-title {
     font-size: 20px;
     letter-spacing: -0.3px;
   }
   
   .asset-type {
     font-size: 10px;
     padding: 2px 6px;
     letter-spacing: 0.8px;
   }
  
  .placeholder-icon i {
    font-size: 48px;
  }
  
  .placeholder-text {
    font-size: 14px;
  }
  
  .image-placeholder {
    border-radius: 0 0 12px 12px;
    gap: 12px;
  }
  
  .back-btn {
    top: 32px;
    left: 12px;
    width: 40px;
    height: 40px;
  }
  
  .back-btn i {
    font-size: 14px;
  }
}

/* 加载和错误状态 */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-container i {
  font-size: 32px;
  color: var(--primary-gold);
  margin-bottom: 16px;
}

.error-container i {
  font-size: 32px;
  color: var(--accent-red);
  margin-bottom: 16px;
}

.retry-btn {
  background: var(--gradient-primary);
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 16px;
}

/* 详情内容 */
.detail-content {
  padding-bottom: 95px; /* 为底部固定区域预留更多空间 */
}

/* 介绍图片样式 */
.intro-images {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.intro-image-container {
  position: relative;
  overflow: hidden;
  border-radius: 16px;
  background: var(--bg-secondary);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.intro-image {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.4s ease;
}

.intro-image-container:hover .intro-image {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(200, 134, 13, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.intro-image-container:hover .image-overlay {
  opacity: 1;
}

/* 基本信息 */
.basic-info {
  padding: 20px 16px;
  background: linear-gradient(135deg, rgba(26, 26, 37, 0.6) 0%, rgba(37, 37, 48, 0.4) 100%);
  margin: 0 12px;
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  position: relative;
  overflow: hidden;
}

.basic-info::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-gold), transparent);
  opacity: 0.6;
}

.status-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 12px;
}

.status-badge.on-sale {
  background: var(--accent-green);
  color: white;
}

.status-badge.sold-out {
  background: var(--neutral-600);
  color: white;
}

.status-badge.limited {
  background: var(--primary-gold);
  color: white;
}

.status-badge.presale {
  background: var(--secondary-purple);
  color: white;
}

.status-badge.default {
  background: var(--accent-red);
  color: white;
}




/* 价格信息 */
.price-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: var(--gradient-card);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.price-info, .quantity-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.price-label, .quantity-label {
  font-size: 12px;
  color: var(--neutral-400);
  margin-bottom: 4px;
}

.price-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-gold);
}

.quantity-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--neutral-200);
}

/* 重新设计的资产信息样式 - 统一大卡片设计 */
.asset-info-unified {
  margin: 0 0 16px 0;
  background: linear-gradient(135deg, rgba(212, 165, 116, 0.08) 0%, rgba(212, 165, 116, 0.03) 100%);
  border: 1px solid rgba(212, 165, 116, 0.2);
  border-radius: 16px;
  padding: 16px;
  backdrop-filter: blur(12px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.asset-info-unified::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-gold), transparent);
  opacity: 0.6;
}

/* 信息行样式 */
.info-row {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
  position: relative;
}

.info-row:last-child {
  margin-bottom: 0;
}

/* 行标签样式 */
.row-label {
  font-size: 12px;
  font-weight: 600;
  color: var(--primary-gold);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 60px;
  flex-shrink: 0;
  padding-top: 2px;
  text-align: right;
  position: relative;
}

.row-label::after {
  content: '';
  position: absolute;
  right: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 2px;
  height: 2px;
  background: var(--primary-gold);
  border-radius: 50%;
  opacity: 0.6;
}

/* 行内容样式 */
.row-content {
  flex: 1;
  display: flex;
  align-items: center;
}

/* 发行方信息样式 - 已更新到上方 */

.issuer-logo {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
}

.issuer-logo .issuer-logo-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.issuer-logo .issuer-logo-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gradient-primary);
  color: white;
  font-size: 14px;
  font-weight: 700;
}

.issuer-info {
  flex: 1;
  min-width: 0;
}

.issuer-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.issuer-subtitle {
  font-size: 11px;
  color: var(--primary-gold);
  font-weight: 500;
}

.verified-badge {
  color: var(--primary-gold);
  font-size: 16px;
  opacity: 0.8;
  flex-shrink: 0;
}

/* 分类信息样式 */
.category-row .category-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  width: 100%;
}

.category-tag {
  display: inline-flex;
  align-items: center;
  padding: 0;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  position: relative;
  font-size: 0;
}

.category-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.category-tag:hover::before {
  left: 100%;
}

.category-tag:hover {
  transform: translateY(-1px);
}

/* 标签信息样式 */
.tags-row .keyword-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  width: 100%;
}

.keyword-tag {
  display: inline-block;
  padding: 4px 8px;
  background: rgba(212, 165, 116, 0.15);
  color: var(--primary-gold-light);
  border: 1px solid rgba(212, 165, 116, 0.4);
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
}

.keyword-tag:hover {
  background: rgba(212, 165, 116, 0.25);
  border-color: var(--primary-gold);
  box-shadow: 0 2px 8px rgba(212, 165, 116, 0.3);
  transform: translateY(-1px);
}

/* 分类标签样式 */
.category-tag {
  background: none;
  color: #ffffff;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  display: inline-flex !important;
  align-items: center;
  padding: 0;
  border-radius: 6px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.category-tag:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* 类型样式 - 蓝色标签名+灰色值区域 */
.tag-type .tag-label {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: #ffffff;
  font-size: 11px;
  font-weight: 700;
  padding: 6px 8px;
  margin: 0;
  opacity: 1;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tag-type .tag-value {
  background: rgba(60, 60, 60, 0.9);
  color: #ffffff;
  padding: 6px 10px;
  font-size: 11px;
  font-weight: 500;
}

.tag-type:hover .tag-label {
  background: linear-gradient(135deg, #3d9cee 0%, #00e2ee 100%);
}

.tag-type:hover .tag-value {
  background: rgba(70, 70, 70, 0.9);
}

/* 系列样式 - 绿色标签名+灰色值区域 */
.tag-series .tag-label {
  background: linear-gradient(135deg, #90a955 0%, #7a9440 100%);
  color: #ffffff;
  font-size: 11px;
  font-weight: 700;
  padding: 6px 8px;
  margin: 0;
  opacity: 1;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tag-series .tag-value {
  background: rgba(60, 60, 60, 0.9);
  color: #ffffff;
  padding: 6px 10px;
  font-size: 11px;
  font-weight: 500;
}

.tag-series:hover .tag-label {
  background: linear-gradient(135deg, #9fb955 0%, #849a40 100%);
}

.tag-series:hover .tag-value {
  background: rgba(70, 70, 70, 0.9);
}

/* 可点击的系列标签样式 */
.tag-series.clickable {
  cursor: pointer;
}

.tag-series.clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(144, 169, 85, 0.3);
}

.tag-series.clickable:active {
  transform: translateY(0);
}

/* 专区样式 - 紫色标签名+灰色值区域 */
.tag-zone .tag-label {
  background: linear-gradient(135deg, #a855f7 0%, #9333ea 100%);
  color: #ffffff;
  font-size: 11px;
  font-weight: 700;
  padding: 6px 8px;
  margin: 0;
  opacity: 1;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tag-zone .tag-value {
  background: rgba(60, 60, 60, 0.9);
  color: #ffffff;
  padding: 6px 10px;
  font-size: 11px;
  font-weight: 500;
}

.tag-zone:hover .tag-label {
  background: linear-gradient(135deg, #b865f7 0%, #a343ea 100%);
}

.tag-zone:hover .tag-value {
  background: rgba(70, 70, 70, 0.9);
}

/* 可点击的专区标签样式 */
.tag-zone.clickable {
  cursor: pointer;
}

.tag-zone.clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(168, 85, 247, 0.3);
}

.tag-zone.clickable:active {
  transform: translateY(0);
}

/* 通用标签样式 */
.tag-label {
  font-size: 11px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

.tag-value {
  font-size: 11px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

/* 响应式优化 */
@media (max-width: 480px) {
  .asset-info-unified {
    margin: 0 0 12px 0;
    padding: 12px;
    border-radius: 12px;
  }
  
  .info-row {
    gap: 12px;
    margin-bottom: 12px;
  }
  
  .row-label {
    font-size: 11px;
    min-width: 50px;
    padding-top: 1px;
  }
  
  .row-label::after {
    right: -6px;
    width: 1.5px;
    height: 1.5px;
  }
  
  /* 移动端发行方样式调整 */
  .issuer-row .row-content {
    gap: 6px;
  }
  
  .issuer-row .issuer-card {
    gap: 10px;
    padding: 6px;
    border-radius: 6px;
  }
  
  .issuer-logo {
    width: 32px;
    height: 32px;
    border-radius: 6px;
  }
  
  .issuer-logo .issuer-logo-placeholder {
    font-size: 12px;
  }
  
  .issuer-name {
    font-size: 13px;
  }
  
  .issuer-subtitle {
    font-size: 10px;
  }
  
  .verified-badge {
    font-size: 14px;
  }
  
  /* 移动端分类标签调整 */
  .category-row .category-tags {
    gap: 5px;
  }
  
  .category-tag .tag-label {
    font-size: 10px;
    padding: 5px 7px;
    letter-spacing: 0.3px;
  }
  
  .category-tag .tag-value {
    padding: 5px 8px;
    font-size: 10px;
    max-width: 80px;
  }
  
  /* 移动端关键字标签调整 */
  .tags-row .keyword-tags {
    gap: 4px;
  }
  
  .keyword-tag {
    padding: 3px 6px;
    font-size: 9px;
    border-radius: 10px;
  }
}

/* 详细信息 */
.detail-info {
  padding: 0 16px;
  margin-bottom: 20px;
}

.info-card {
  background: var(--gradient-card);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 16px;
  overflow: hidden;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--neutral-200);
  margin: 0;
  padding: 16px 16px 0 16px;
}

.info-list {
  padding: 12px 16px 16px 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 14px;
  color: var(--neutral-400);
}

.info-value {
  font-size: 14px;
  color: var(--neutral-200);
  font-weight: 500;
}

/* 描述部分 */
.description-section {
  padding: 0 20px;
  margin-bottom: 24px;
}

.section-header {
  margin-bottom: 20px;
}

.section-title {
  font-size: 20px;
  font-weight: 700;
  color: var(--neutral-100);
  margin: 0 0 12px 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.section-divider {
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, var(--primary-gold) 0%, transparent 50%, var(--primary-gold) 100%);
  opacity: 0.3;
}

.description-content {
  background: var(--gradient-card);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 16px;
  font-size: 14px;
  line-height: 1.6;
  color: var(--neutral-200);
}

/* 购买须知部分 */
.purchase-notice-section {
  padding: 0 20px;
  margin-bottom: 24px;
}

.notice-content {
  background: linear-gradient(135deg, rgba(244, 162, 97, 0.08) 0%, rgba(244, 162, 97, 0.03) 100%);
  border: 1px solid rgba(244, 162, 97, 0.2);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(12px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.notice-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
  opacity: 0.6;
}

.notice-content::after {
  content: '';
  position: absolute;
  top: 8px;
  left: 8px;
  width: 4px;
  height: 4px;
  background: var(--accent-color);
  border-radius: 50%;
  opacity: 0.8;
}

.notice-text {
  font-size: 14px;
  line-height: 1.7;
  color: var(--text-secondary);
  text-align: justify;
  position: relative;
  z-index: 2;
}



/* 响应式优化 */
@media (max-width: 480px) {
  .purchase-notice-section {
    padding: 0 16px;
    margin-bottom: 20px;
  }
  
  .notice-content {
    padding: 16px;
    border-radius: 12px;
  }
  
  .notice-text {
    font-size: 13px;
    line-height: 1.6;
  }
  

}

/* 底部固定区域 */
.bottom-fixed-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(15, 15, 21, 0.95), rgba(26, 26, 37, 0.85));
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  padding: 12px 16px 16px 16px;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.4);
}

/* 价格显示 - 去掉框和特效 */
.price-display {
  flex: 0 0 auto;
  width: 120px;
  padding: 12px 16px;
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.price-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  justify-content: center;
  position: relative;
  z-index: 2;
}

.price-header {
  display: flex;
  align-items: center;
  gap: 6px;
}

.price-icon {
  font-size: 14px;
  color: var(--primary-gold-light);
  opacity: 0.9;
}

.price-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.price-label {
  color: var(--neutral-300);
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
}

.price-value {
  font-size: 18px;
  color: var(--primary-gold-light);
  font-weight: 800;
  line-height: 1;
  text-align: center;
}



/* 操作按钮区 */
.action-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
}

.action-btn {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 14px;
  font-size: 15px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-height: 48px;
  position: relative;
  overflow: hidden;
}

.action-btn.primary {
  background: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-light) 50%, var(--primary-gold) 100%);
  background-size: 200% 100%;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  box-shadow: 0 4px 12px rgba(200, 134, 13, 0.4);
  animation: gradient-shift 3s ease infinite;
}

.action-btn.primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.action-btn.primary:hover::before {
  left: 100%;
}

.action-btn.primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(200, 134, 13, 0.5);
  background-position: 100% 0;
}

.action-btn.primary:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(200, 134, 13, 0.4);
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: 0 4px 12px rgba(200, 134, 13, 0.2) !important;
}

.action-btn:disabled:hover {
  transform: none !important;
  box-shadow: 0 4px 12px rgba(200, 134, 13, 0.2) !important;
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* 倒计时显示 */
.countdown-display {
  flex: 1;
  background: linear-gradient(135deg, rgba(200, 134, 13, 0.12) 0%, rgba(200, 134, 13, 0.06) 100%);
  border: 1px solid rgba(200, 134, 13, 0.4);
  border-radius: 14px;
  padding: 12px 16px;
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(8px);
}

.countdown-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  justify-content: center;
  position: relative;
  z-index: 2;
}

.countdown-header {
  display: flex;
  align-items: center;
  gap: 6px;
}

.countdown-icon {
  font-size: 14px;
  color: var(--primary-gold-light);
  opacity: 0.9;
}

.countdown-text {
  color: var(--neutral-300);
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.countdown-time {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-unit {
  display: flex;
  align-items: center;
  gap: 2px;
  padding: 4px 6px;
  background: rgba(200, 134, 13, 0.1);
  border: 1px solid rgba(200, 134, 13, 0.3);
  border-radius: 6px;
  min-width: 32px;
  justify-content: center;
}

.time-number {
  color: var(--primary-gold-light);
  font-size: 14px;
  font-weight: 800;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  line-height: 1;
}

.time-label {
  color: var(--neutral-400);
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  line-height: 1;
  margin-left: 1px;
}

.countdown-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120%;
  height: 120%;
  border: 1px solid rgba(200, 134, 13, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.8;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
    opacity: 0.4;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.3);
    opacity: 0;
  }
}

/* 图片预览模态框 */
.image-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.modal-content {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
}

.modal-close {
  position: absolute;
  top: -50px;
  right: 0;
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  z-index: 1001;
}

.modal-image {
  max-width: 100%;
  max-height: 100%;
  border-radius: 12px;
}



/* 响应式设计 */
@media (max-width: 480px) {
  .price-section {
    flex-direction: column;
    gap: 16px;
  }
  
  .price-info, .quantity-info {
    width: 100%;
  }
  
  .action-section {
    flex-direction: column;
  }
  
  .action-btn {
    width: 100%;
  }
}

/* 发行方信息样式 */
.issuer-row .row-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.issuer-row .issuer-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  width: 100%;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.issuer-row .issuer-card:hover {
  background: rgba(255, 255, 255, 0.06);
  border-color: rgba(212, 165, 116, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}


</style> 